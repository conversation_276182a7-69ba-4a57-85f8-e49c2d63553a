{% extends "base.html" %}

{% block title %}شاشة عرض المستودع الأول{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/display-screens-modern.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/custom-display.css') }}">
{% endblock %}

{% block content %}
<!-- Aplicamos la clase display-screen sin sobrescribir el tema del body -->
<div class="display-screen">
    <!-- Header -->
    <div class="display-header">
        <div class="header-content">
            <div class="logo-section">
                <img src="{{ url_for('static', filename='images/KSA.png') }}" alt="شعار" class="logo">
                <h1>{{ warehouse.name }}</h1>
            </div>
            <div class="time-section">
                <div class="current-time" id="current-time"></div>
                <div class="current-date" id="current-date"></div>
            </div>
        </div>
        <div class="status-banner">
            <div class="status-item">
                <i class="fas fa-users"></i>
                <span>الأفراد المتواجدون: <strong>{{ active_personnel }}</strong></span>
            </div>
            <div class="status-item">
                <i class="fas fa-crosshairs"></i>
                <span>الأسلحة النشطة: <strong>{{ weapon_status.active }}</strong></span>
            </div>
            <div class="status-item">
                <i class="fas fa-sync"></i>
                <span>آخر تحديث: <strong id="last-update"></strong></span>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="display-cards">
        <!-- Personnel Card -->
        <div class="display-card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-users"></i>
                    الأفراد
                </h2>
                <span class="stat-value">{{ personnel_count }}</span>
            </div>
            <div class="card-body">
                <div class="progress-container">
                    <div class="progress-label">
                        <span>نشط</span>
                        <span>{{ active_personnel }}</span>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar active" style="width: {{ (active_personnel / personnel_count * 100) if personnel_count > 0 else 0 }}%"></div>
                    </div>
                </div>
                <div class="progress-container">
                    <div class="progress-label">
                        <span>إجازة</span>
                        <span>{{ leave_personnel }}</span>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar leave" style="width: {{ (leave_personnel / personnel_count * 100) if personnel_count > 0 else 0 }}%"></div>
                    </div>
                </div>
                <div class="progress-container">
                    <div class="progress-label">
                        <span>مهمة</span>
                        <span>{{ mission_personnel }}</span>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar mission" style="width: {{ (mission_personnel / personnel_count * 100) if personnel_count > 0 else 0 }}%"></div>
                    </div>
                </div>
                <div class="progress-container">
                    <div class="progress-label">
                        <span>دورة</span>
                        <span>{{ cycle_personnel|default(0) }}</span>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar cycle" style="width: {{ (cycle_personnel|default(0) / personnel_count * 100) if personnel_count > 0 else 0 }}%"></div>
                    </div>
                </div>
                <div class="progress-container">
                    <div class="progress-label">
                        <span>مستلم</span>
                        <span>{{ stoped_personnel }}</span>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar recipient" style="width: {{ (stoped_personnel / personnel_count * 100) if personnel_count > 0 else 0 }}%"></div>
                    </div>
                </div>
                <div class="progress-container">
                    <div class="progress-label">
                        <span>رماية</span>
                        <span>{{ shooting_personnel|default(0) }}</span>
                    </div>
                    <div class="progress-bar-container">
                        <div class="progress-bar shooting" style="width: {{ (shooting_personnel|default(0) / personnel_count * 100) if personnel_count > 0 else 0 }}%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Weapons Card -->
        <div class="display-card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-crosshairs"></i>
                    الأسلحة
                </h2>
                <span class="stat-value">{{ weapons_count }}</span>
            </div>
            <div class="card-body">
                <div class="donut-chart">
                    <canvas id="weaponsChart" width="200" height="200"></canvas>
                    <div class="donut-chart-inner">
                        <div class="donut-chart-number">{{ weapons_count }}</div>
                        <div class="donut-chart-label">الإجمالي</div>
                    </div>
                </div>
                <div class="chart-legend">
                    <div class="legend-item">
                        <div class="legend-color-label">
                            <div class="legend-color active"></div>
                            <div class="legend-label">نشط</div>
                        </div>
                        <div class="legend-count">{{ weapon_status.active }}</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color-label">
                            <div class="legend-color leave"></div>
                            <div class="legend-label">إجازة</div>
                        </div>
                        <div class="legend-count">{{ weapon_status.leave }}</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color-label">
                            <div class="legend-color mission"></div>
                            <div class="legend-label">مهمة</div>
                        </div>
                        <div class="legend-count">{{ weapon_status.mission }}</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color-label">
                            <div class="legend-color maintenance"></div>
                            <div class="legend-label">صيانة</div>
                        </div>
                        <div class="legend-count">{{ weapon_status.maintenance }}</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color-label">
                            <div class="legend-color cycle"></div>
                            <div class="legend-label">دورة</div>
                        </div>
                        <div class="legend-count">{{ weapon_status.cycle }}</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color-label">
                            <div class="legend-color vacant"></div>
                            <div class="legend-label">شاغر</div>
                        </div>
                        <div class="legend-count">{{ weapon_status.vacant }}</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color-label">
                            <div class="legend-color recipient"></div>
                            <div class="legend-label">مستلم</div>
                        </div>
                        <div class="legend-count">{{ weapon_status.recipient|default(0) }}</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color-label">
                            <div class="legend-color shooting"></div>
                            <div class="legend-label">رماية</div>
                        </div>
                        <div class="legend-count">{{ weapon_status.shooting|default(0) }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Transactions Card -->
        <div class="display-card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-exchange-alt"></i>
                    آخر المعاملات
                </h2>
                <button class="panel-toggle-btn" data-target="transactions-body" title="طي">
                    <i class="fas fa-chevron-up"></i>
                </button>
            </div>
            <div class="card-body" id="transactions-body">
                <ul class="transactions-list" id="transactions-list">
                    {% if recent_transactions %}
                        {% for transaction in recent_transactions %}
                            <li class="transaction-item">
                                <div class="transaction-icon {{ transaction.transaction_type }}">
                                    {% if transaction.transaction_type == 'checkout' %}
                                        <i class="fas fa-arrow-right"></i>
                                    {% elif transaction.transaction_type == 'return' %}
                                        <i class="fas fa-arrow-left"></i>
                                    {% else %}
                                        <i class="fas fa-exchange-alt"></i>
                                    {% endif %}
                                </div>
                                <div class="transaction-content">
                                    <div class="transaction-title">
                                        {% if transaction.transaction_type == 'checkout' %}
                                            تسليم سلاح
                                        {% elif transaction.transaction_type == 'return' %}
                                            إعادة سلاح
                                        {% else %}
                                            نقل سلاح
                                        {% endif %}
                                    </div>
                                    <div class="transaction-details">
                                        {% if transaction.weapon %}
                                            {{ transaction.weapon.name }} ({{ transaction.weapon.serial_number }})
                                        {% endif %}
                                        {% if transaction.personnel %}
                                            - {{ transaction.personnel.name }}
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="transaction-time">
                                    {{ transaction.timestamp.strftime('%H:%M') }}
                                </div>
                            </li>
                        {% endfor %}
                    {% else %}
                        <li class="transaction-item">
                            <div class="transaction-content">
                                <div class="transaction-title text-center">لا توجد معاملات حديثة</div>
                            </div>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>

    <div class="last-update">
        آخر تحديث للبيانات: <span id="data-last-update"></span>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Update date and time
        function updateDateTime() {
            const now = new Date();
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };

            document.getElementById('current-time').textContent = now.toLocaleTimeString('ar-SA');
            document.getElementById('current-date').textContent = now.toLocaleDateString('ar-SA', options);
            document.getElementById('last-update').textContent = now.toLocaleTimeString('ar-SA');
        }

        // Initial update
        updateDateTime();

        // Update every second
        setInterval(updateDateTime, 1000);

        // Initialize charts
        const weaponsCtx = document.getElementById('weaponsChart').getContext('2d');
        const weaponsChart = new Chart(weaponsCtx, {
            type: 'doughnut',
            data: {
                labels: ['نشط', 'إجازة', 'مهمة', 'صيانة', 'دورة', 'شاغر', 'مستلم', 'رماية'],
                datasets: [{
                    data: [
                        {{ weapon_status.active }},
                        {{ weapon_status.leave }},
                        {{ weapon_status.mission }},
                        {{ weapon_status.maintenance }},
                        {{ weapon_status.cycle }},
                        {{ weapon_status.vacant }},
                        {{ weapon_status.recipient|default(0) }},
                        {{ weapon_status.shooting|default(0) }}
                    ],
                    backgroundColor: [
                        '#28a745', // Success color for active
                        '#ffc107', // Warning color for leave (تم توحيد اللون)
                        '#f97316', // Orange color for mission
                        '#FAAFBE', // Pink color for maintenance
                        '#ef4444', // Red color for cycle
                        '#6b7280',  // Gray color for vacant
                        '#0d6efd',   // Blue color for recipient
                        '#C8BBBE'   // Lavender Blush3 color for shooting
                    ],
                    borderWidth: 0,
                    hoverOffset: 4
                }]
            },
            options: {
                cutout: '70%',
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: true
                    }
                }
            }
        });

        // Periodically refresh the data
        function refreshData() {
            fetch('/api/warehouse/{{ warehouse.id }}/stats')
                .then(response => response.json())
                .then(data => {
                    // Update data last update time
                    const now = new Date();
                    document.getElementById('data-last-update').textContent = now.toLocaleTimeString('ar-SA');

                    // Update weapons chart
                    if (weaponsChart) {
                        weaponsChart.data.datasets[0].data = [
                            data.weapon_status.active,
                            data.weapon_status.leave,
                            data.weapon_status.mission,
                            data.weapon_status.maintenance,
                            data.weapon_status.cycle,
                            data.weapon_status.vacant,
                            data.weapon_status.recipient || 0,
                            data.weapon_status.shooting || 0
                        ];
                        weaponsChart.update();
                    }

                    // Update personnel status bars
                    const totalPersonnel = data.personnel_count;

                    // Update active personnel
                    const activeBar = document.querySelector('.progress-bar.active');
                    if (activeBar) {
                        activeBar.style.width = `${(data.personnel_status.active / totalPersonnel * 100) || 0}%`;
                        activeBar.parentElement.previousElementSibling.lastElementChild.textContent = data.personnel_status.active;
                    }

                    // Update leave personnel
                    const leaveBar = document.querySelector('.progress-bar.leave');
                    if (leaveBar) {
                        leaveBar.style.width = `${(data.personnel_status.leave / totalPersonnel * 100) || 0}%`;
                        leaveBar.parentElement.previousElementSibling.lastElementChild.textContent = data.personnel_status.leave;
                    }

                    // Update mission personnel
                    const missionBar = document.querySelector('.progress-bar.mission');
                    if (missionBar) {
                        missionBar.style.width = `${(data.personnel_status.mission / totalPersonnel * 100) || 0}%`;
                        missionBar.parentElement.previousElementSibling.lastElementChild.textContent = data.personnel_status.mission;
                    }

                    // Update cycle personnel
                    const cycleBar = document.querySelector('.progress-bar.cycle');
                    if (cycleBar) {
                        cycleBar.style.width = `${(data.personnel_status.cycle / totalPersonnel * 100) || 0}%`;
                        cycleBar.parentElement.previousElementSibling.lastElementChild.textContent = data.personnel_status.cycle || 0;
                    }

                    // Update recipient personnel
                    const recipientBar = document.querySelector('.progress-bar.recipient');
                    if (recipientBar) {
                        recipientBar.style.width = `${(data.personnel_status.recipient / totalPersonnel * 100) || 0}%`;
                        recipientBar.parentElement.previousElementSibling.lastElementChild.textContent = data.personnel_status.recipient;
                    }

                    // Update shooting personnel
                    const shootingBar = document.querySelector('.progress-bar.shooting');
                    if (shootingBar) {
                        shootingBar.style.width = `${(data.personnel_status.shooting / totalPersonnel * 100) || 0}%`;
                        shootingBar.parentElement.previousElementSibling.lastElementChild.textContent = data.personnel_status.shooting || 0;
                    }

                    // Update status banner
                    document.querySelector('.status-item:nth-child(1) strong').textContent = data.personnel_status.active;
                    document.querySelector('.status-item:nth-child(2) strong').textContent = data.weapon_status.active;

                    // Update total counts
                    document.querySelector('.card-header .stat-value').textContent = data.personnel_count;
                    document.querySelectorAll('.card-header .stat-value')[1].textContent = data.weapons_count;

                    // Update donut chart inner number
                    document.querySelector('.donut-chart-number').textContent = data.weapons_count;

                    // Update legend counts
                    const legendCounts = document.querySelectorAll('.legend-count');
                    legendCounts[0].textContent = data.weapon_status.active;
                    legendCounts[1].textContent = data.weapon_status.leave;
                    legendCounts[2].textContent = data.weapon_status.mission;
                    legendCounts[3].textContent = data.weapon_status.maintenance;
                    legendCounts[4].textContent = data.weapon_status.cycle;
                    legendCounts[5].textContent = data.weapon_status.vacant;
                    legendCounts[6].textContent = data.weapon_status.recipient || 0;
                    legendCounts[7].textContent = data.weapon_status.shooting || 0;
                })
                .catch(error => {
                    console.error('Error fetching updated data:', error);
                });
        }

        // Refresh every 30 seconds
        setInterval(refreshData, 30000);

        // Initial data refresh
        refreshData();
    });
</script>
{% endblock %}
