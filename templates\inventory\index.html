{% extends "base.html" %}

{% block title %}المخزون{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0">
                    <i class="fas fa-boxes text-primary"></i>
                    إدارة المخزون
                </h2>
                <div class="btn-group">
                    <a href="{{ url_for('inventory_management.add_item') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة صنف جديد
                    </a>
                    <a href="{{ url_for('inventory_management.add_transaction') }}" class="btn btn-success">
                        <i class="fas fa-exchange-alt"></i> معاملة جديدة
                    </a>
                    <a href="{{ url_for('inventory_management.reports') }}" class="btn btn-info">
                        <i class="fas fa-chart-bar"></i> التقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ total_items }}</h4>
                            <p class="mb-0">إجمالي الأصناف</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ low_stock_items }}</h4>
                            <p class="mb-0">أصناف منخفضة المخزون</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ expired_items }}</h4>
                            <p class="mb-0">أصناف منتهية الصلاحية</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-times fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ total_items - low_stock_items - expired_items }}</h4>
                            <p class="mb-0">أصناف متوفرة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie"></i>
                        إحصائيات الفئات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for category, count in categories_stats.items() %}
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-primary">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                {{ category }}
                                            </div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {{ count }} صنف
                                            </div>
                                            <a href="{{ url_for('inventory_management.items', category=category) }}" class="btn btn-sm btn-outline-primary mt-2">
                                                <i class="fas fa-eye"></i> عرض الأصناف
                                            </a>
                                        </div>
                                        <div class="col-auto">
                                            {% if category == 'ملبوسات' or category == 'ملبوسات عسكرية' %}
                                                <i class="fas fa-tshirt fa-2x text-success"></i>
                                            {% elif category == 'معدات' or category == 'معدات عسكرية' %}
                                                <i class="fas fa-tools fa-2x text-info"></i>
                                            {% elif category == 'ذخيرة' %}
                                                <i class="fas fa-bomb fa-2x text-danger"></i>
                                            {% else %}
                                                <i class="fas fa-box fa-2x text-gray-300"></i>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt"></i>
                        الإجراءات السريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('inventory_management.items') }}" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-list"></i><br>
                                عرض جميع الأصناف
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('inventory_management.transactions') }}" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-exchange-alt"></i><br>
                                المعاملات
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('inventory_management.reports') }}" class="btn btn-outline-secondary btn-block">
                                <i class="fas fa-chart-bar"></i><br>
                                التقارير
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('inventory_management.items', status='منخفض المخزون') }}" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-exclamation-triangle"></i><br>
                                المخزون المنخفض
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Add Items Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-plus-circle"></i>
                        إضافة أصناف جديدة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-success h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-tshirt fa-3x text-success mb-3"></i>
                                    <h5 class="card-title">الملبوسات العسكرية</h5>
                                    <p class="card-text text-muted">إضافة ملبوسات عسكرية جديدة مثل الزي الرسمي، الأحذية، القبعات</p>
                                    <a href="{{ url_for('inventory_management.add_item') }}?category=ملبوسات" class="btn btn-success">
                                        <i class="fas fa-plus"></i> إضافة ملبوسات
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-info h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-tools fa-3x text-info mb-3"></i>
                                    <h5 class="card-title">المعدات العسكرية</h5>
                                    <p class="card-text text-muted">إضافة معدات عسكرية مثل الخوذات، السترات الواقية، المعدات التقنية</p>
                                    <a href="{{ url_for('inventory_management.add_item') }}?category=معدات" class="btn btn-info">
                                        <i class="fas fa-plus"></i> إضافة معدات
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-danger h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-bomb fa-3x text-danger mb-3"></i>
                                    <h5 class="card-title">الذخيرة</h5>
                                    <p class="card-text text-muted">إضافة أنواع مختلفة من الذخيرة والمواد المتفجرة</p>
                                    <a href="{{ url_for('inventory_management.add_item') }}?category=ذخيرة" class="btn btn-danger">
                                        <i class="fas fa-plus"></i> إضافة ذخيرة
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// تحديث الصفحة كل 5 دقائق للحصول على أحدث الإحصائيات
setTimeout(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}
