{% extends "base.html" %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/warehouse-details.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/warehouse-list.css') }}">
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>
            تفاصيل المستودع: {{ warehouse.name }}
        </h3>
    </div>
    <div class="col-md-4 text-right">
        {% if current_user.role == 'admin' %}
        <a href="{{ url_for('warehouse.edit_warehouse', warehouse_id=warehouse.id) }}" class="btn btn-primary">
            <i class="fas fa-edit"></i> تعديل البيانات
        </a>
        {% endif %}
        <a href="{{ url_for('warehouse.manage') }}" class="btn btn-outline-secondary ml-2">
            <i class="fas fa-arrow-right"></i> العودة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> معلومات المستودع</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4 font-weight-bold">الاسم:</div>
                    <div class="col-md-8">{{ warehouse.name }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 font-weight-bold">الموقع:</div>
                    <div class="col-md-8">{{ warehouse.location or '-' }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 font-weight-bold">الوصف:</div>
                    <div class="col-md-8">{{ warehouse.description or '-' }}</div>
                </div>
                <div class="row">
                    <div class="col-md-4 font-weight-bold">تاريخ الإنشاء:</div>
                    <div class="col-md-8">{{ warehouse.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> إحصائيات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        عدد الأسلحة
                        <span class="badge badge-primary badge-pill">{{ warehouse.weapons.count() }}</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        عدد الأفراد
                        <span class="badge badge-info badge-pill">{{ warehouse.personnel.count() }}</span>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        عدد المعاملات
                        <span class="badge badge-success badge-pill">{{ warehouse.activity_logs.count() }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 ml-auto"><i class="fas fa-crosshairs"></i> الأسلحة في المستودع</h5>
                </div>
                <a href="{{ url_for('weapons.warehouse_weapons', warehouse_id=warehouse.id) }}"
                    class="btn btn-sm btn-outline-primary btn-view-all">
                    عرض الكل
                </a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الرقم التسلسلي</th>
                                <th>الاسم</th>
                                <th>رقم السلاح</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for weapon in warehouse.weapons.limit(5).all() %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ weapon.serial_number }}</td>
                                <td>{{ weapon.name }}</td>
                                <td>
                                    {% if weapon.type == 'pistol' %}
                                    مسدس
                                    {% elif weapon.type == 'rifle' %}
                                    بندقية
                                    {% elif weapon.type == 'sniper' %}
                                    قناص
                                    {% elif weapon.type == 'machine_gun' %}
                                    رشاش
                                    {% else %}
                                    {{ weapon.type }}
                                    {% endif %}
                                </td>
                                <td>
                                    {% if weapon.status == 'نشط' %}
                                    <span class="badge badge-success">{{ weapon.status }}</span>
                                    {% elif weapon.status == 'إجازة' %}
                                    <span class="badge badge-warning">{{ weapon.status }}</span>
                                    {% elif weapon.status == 'مهمة' %}
                                    <span class="badge badge-mission">{{ weapon.status }}</span>
                                    {% elif weapon.status == 'صيانة' %}
                                    <span class="badge badge-maintenance">{{ weapon.status }}</span>
                                    {% elif weapon.status == 'تالف' %}
                                    <span class="badge badge-primary">{{ weapon.status }}</span>
                                    {% elif weapon.status == 'دورة' %}
                                    <span class="badge badge-danger">{{ weapon.status }}</span>
                                    {% elif weapon.status == 'شاغر' %}
                                    <span class="badge badge-dark">{{ weapon.status }}</span>
                                    {% elif weapon.status == 'مستلم' %}
                                    <span class="badge badge-recipient">{{ weapon.status }}</span>
                                    {% else %}
                                    <span class="badge badge-secondary">{{ weapon.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('weapons.details', weapon_id=weapon.id) }}"
                                        class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center py-3">
                                    <div class="alert alert-info mb-0">لا توجد أسلحة في هذا المستودع</div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 ml-auto"><i class="fas fa-users"></i> الأفراد في المستودع</h5>
                </div>
                <a href="{{ url_for('personnel.warehouse_personnel', warehouse_id=warehouse.id) }}"
                    class="btn btn-sm btn-outline-info btn-view-all">
                    عرض الكل
                </a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الرقم العسكري</th>
                                <th>الاسم</th>
                                <th>الرتبة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for person in warehouse.personnel.limit(5).all() %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ person.personnel_id }}</td>
                                <td>{{ person.name }}</td>
                                <td>{{ person.rank }}</td>
                                <td>
                                    {% if person.status == 'نشط' %}
                                    <span class="badge badge-success">{{ person.status }}</span>
                                    {% elif person.status == 'إجازة' %}
                                    <span class="badge badge-warning">{{ person.status }}</span>
                                    {% elif person.status == 'مهمة' %}
                                    <span class="badge badge-mission">{{ person.status }}</span>
                                    {% elif person.status == 'مستلم' %}
                                    <span class="badge badge-primary">{{ person.status }}</span>
                                    {% else %}
                                    <span class="badge badge-secondary">{{ person.status }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('personnel.details', personnel_id=person.id) }}"
                                        class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center py-3">
                                    <div class="alert alert-info mb-0">لا يوجد أفراد في هذا المستودع</div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <!-- تم إزالة قسم الأجهزة لأنها أصبحت مستقلة عن المستودعات -->
    </div>
</div>
{% endblock %}