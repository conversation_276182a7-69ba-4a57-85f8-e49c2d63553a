{% extends "base.html" %}

{% block title %}كشف الاستلامات{% endblock %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/receipts.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h3 style="color: var(--text-primary) !important;">
                <i class="fas fa-clipboard-list"></i> كشف الاستلامات
            </h3>
            <p class="text-muted">إنشاء وإدارة كشوف استلام المناوبات</p>
        </div>
        <div class="col-md-4 text-right">
            <button type="button" class="btn btn-success me-2" onclick="saveReceipt()">
                <i class="fas fa-save"></i> حفظ
            </button>
            <button type="button" class="btn btn-success" onclick="exportToExcel()">
                <i class="fas fa-file-excel"></i> تصدير
            </button>
        </div>
    </div>

    <!-- Receipt Form -->
    <div class="card">
        <div class="card-header text-center">
            <h4 class="mb-3" style="color: var(--text-primary) !important;">كشف الاستلامات</h4>
            <div class="row">
                <div class="col-md-3">
                    <label>اليوم:</label>
                    <input type="text" id="dayName" class="form-control" readonly>
                </div>
                <div class="col-md-3">
                    <label>التاريخ الهجري:</label>
                    <input type="text" id="hijriDate" class="form-control" readonly>
                </div>
                <div class="col-md-3">
                    <label>التاريخ الميلادي:</label>
                    <input type="text" id="gregorianDate" class="form-control" readonly>
                </div>
                <div class="col-md-3">
                    <label>رقم الكشف:</label>
                    <input type="text" id="receiptNumber" class="form-control" readonly>
                </div>
            </div>
        </div>

        <div class="card-body">
            <!-- Table Controls -->
            <div class="table-controls mb-3">
                <div class="row">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addColumn()">
                            <i class="fas fa-plus"></i> إضافة عمود
                        </button>
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addRow()">
                            <i class="fas fa-plus"></i> إضافة صف
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm me-2" onclick="resetHeaders()">
                            <i class="fas fa-eraser"></i> تفريغ الكشف
                        </button>
                    </div>
                </div>
            </div>

            <!-- Main Receipt Table -->
            <div class="table-responsive">
                <table class="table table-bordered receipts-table" id="receiptTable">
                    <thead id="tableHeader">
                        <!-- Dynamic header will be generated -->
                    </thead>
                    <tbody id="receiptTableBody">
                        <!-- Dynamic rows will be generated -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Patrol Notes Table -->
    <div class="card mt-4">
        <div class="card-header text-center">
            <h5 class="mb-0" style="color: var(--text-primary) !important;">كشف استلامات الدوريات</h5>
        </div>
        <div class="card-body">
            <!-- Patrol Table Controls -->
            <div class="table-controls mb-3">
                <div class="row">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addPatrolColumn()">
                            <i class="fas fa-plus"></i> إضافة عمود
                        </button>
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addPatrolRow()">
                            <i class="fas fa-plus"></i> إضافة صف
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm me-2" onclick="resetPatrolTable()">
                            <i class="fas fa-eraser"></i> تفريغ الجدول
                        </button>
                    </div>
                </div>
            </div>

            <!-- Patrol Notes Table -->
            <div class="table-responsive">
                <table class="table table-bordered receipts-table" id="patrolTable">
                    <thead id="patrolTableHeader">
                        <!-- Dynamic header will be generated -->
                    </thead>
                    <tbody id="patrolTableBody">
                        <!-- Dynamic rows will be generated -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Shifts Table -->
    <div class="card mt-4">
        <div class="card-header text-center">
            <h5 class="mb-0" style="color: var(--text-primary) !important;">كشف المناوبين</h5>
        </div>
        <div class="card-body">
            <!-- Shifts Table Controls -->
            <div class="table-controls mb-3">
                <div class="row">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addShiftsColumn()">
                            <i class="fas fa-plus"></i> إضافة عمود
                        </button>
                        <button type="button" class="btn btn-success btn-sm me-2" onclick="addShiftsRow()">
                            <i class="fas fa-plus"></i> إضافة صف
                        </button>
                        <button type="button" class="btn btn-secondary btn-sm me-2" onclick="resetShiftsTable()">
                            <i class="fas fa-eraser"></i> تفريغ الجدول
                        </button>
                    </div>
                </div>
            </div>

            <!-- Shifts Table -->
            <div class="table-responsive">
                <table class="table table-bordered receipts-table" id="shiftsTable">
                    <thead id="shiftsTableHeader">
                        <!-- Dynamic header will be generated -->
                    </thead>
                    <tbody id="shiftsTableBody">
                        <!-- Dynamic rows will be generated -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Personnel Search Modal -->
<div class="modal fade" id="personnelSearchModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">🔍 البحث عن الأفراد</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="search-section mb-4">
                    <div class="row">
                        <div class="col-md-8">
                            <label class="form-label">رقم الهوية الوطنية (10 أرقام):</label>
                            <input type="text" class="form-control" id="searchNationalId"
                                   placeholder="أدخل رقم الهوية الوطنية" maxlength="10"
                                   style="color: #000 !important; background-color: #fff !important;"
                                   oninput="searchPersonnelLive(this.value)">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="button" class="btn btn-primary w-100" onclick="searchPersonnel()">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </div>
                </div>

                <div class="search-results" id="searchResults">
                    <!-- سيتم ملء النتائج هنا تلقائياً -->
                </div>

                <div class="personnel-details" id="personnelDetails" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">تفاصيل الفرد</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>الاسم:</strong> <span id="detailName"></span>
                                </div>
                                <div class="col-md-6">
                                    <strong>الرتبة:</strong> <span id="detailRank"></span>
                                </div>
                                <div class="col-md-6">
                                    <strong>الوحدة:</strong> <span id="detailUnit"></span>
                                </div>
                                <div class="col-md-6">
                                    <strong>رقم الهوية:</strong> <span id="detailNationalId"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="insertPersonnelBtn" onclick="insertPersonnel()" disabled>
                    <i class="fas fa-check"></i> إدراج في الخانة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Location Modal -->
<div class="modal fade" id="addLocationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة موقع جديد</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="locationForm">
                    <div class="mb-3">
                        <label class="form-label">اسم الموقع *</label>
                        <input type="text" class="form-control" id="locationName" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">نوع الموقع</label>
                        <select class="form-control" id="locationType">
                            <option value="أمني">أمني</option>
                            <option value="إداري">إداري</option>
                            <option value="حراسة">حراسة</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الوصف</label>
                        <textarea class="form-control" id="locationDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveLocation()">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Print Styles -->
<style>
@media print {
    .btn, .modal, .navbar, .sidebar {
        display: none !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }

    .table {
        font-size: 10px;
    }

    .table th,
    .table td {
        padding: 4px 2px;
        border: 1px solid #000 !important;
    }

    .card {
        page-break-inside: avoid;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .form-control {
        border: none !important;
        background: transparent !important;
        color: black !important;
    }
}

.receipt-table {
    font-size: 12px;
}

.receipt-table th {
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: bold;
    text-align: center;
    vertical-align: middle;
    padding: 8px 4px;
    border: 1px solid var(--border-color);
}

.receipt-table td {
    text-align: center;
    vertical-align: middle;
    padding: 4px 2px;
    border: 1px solid var(--border-color);
}

.receipt-table .form-control {
    border: none;
    background: transparent;
    text-align: center;
    padding: 2px 4px;
    font-size: 11px;
}

.receipt-table .form-control:focus {
    background: var(--bg-tertiary);
    border: 1px solid var(--accent-color);
}

.border-0 {
    border: none !important;
}

.personnel-search {
    position: relative;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-top: none;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-result-item {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid var(--border-color);
}

.search-result-item:hover {
    background: var(--bg-tertiary);
}

.search-result-item:last-child {
    border-bottom: none;
}

/* Table Controls */
.table-controls {
    background: var(--bg-secondary);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

/* Receipt Table */
.receipt-table {
    font-size: 12px;
    margin-bottom: 0;
}

.receipt-table th {
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: bold;
    text-align: center;
    vertical-align: middle;
    padding: 8px 4px;
    border: 1px solid var(--border-color);
    position: relative;
}

.receipt-table td {
    text-align: center;
    vertical-align: middle;
    padding: 4px 2px;
    border: 1px solid var(--border-color);
    position: relative;
}

/* Editable Headers */
.editable-header {
    background: transparent;
    border: none;
    color: var(--text-primary);
    font-weight: bold;
    text-align: center;
    width: 100%;
    padding: 4px;
    outline: none;
    font-size: 11px;
}

.editable-header:focus {
    background: rgba(255, 215, 0, 0.2);
    border: 1px solid var(--accent-color);
    border-radius: 3px;
}

/* Editable Cells */
.editable-cell {
    background: transparent;
    border: none;
    color: var(--text-primary);
    text-align: center;
    width: 100%;
    padding: 4px;
    outline: none;
    font-size: 11px;
    min-height: 25px;
}

.editable-cell:focus {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--accent-color);
    border-radius: 3px;
}

/* Location Select */
.location-select {
    background: var(--bg-primary) !important;
    border: 1px solid var(--border-color) !important;
    color: var(--text-primary) !important;
    text-align: center;
    width: 100%;
    padding: 4px;
    outline: none;
    font-size: 11px;
    border-radius: 3px;
}

.location-select:focus {
    background: var(--bg-secondary) !important;
    border: 1px solid var(--accent-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

.location-select option {
    background: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    padding: 5px;
}

/* Column and Row Controls */
.column-controls {
    position: absolute;
    top: 2px;
    right: 2px;
    display: none;
    gap: 2px;
}

.receipt-table th:hover .column-controls {
    display: flex;
}

.row-controls {
    position: absolute;
    top: 50%;
    right: 2px;
    transform: translateY(-50%);
    display: none;
    gap: 2px;
}

.receipt-table td:hover .row-controls {
    display: flex;
}

/* تم نقل تنسيق أزرار التحكم إلى ملف custom.css */

/* Row Number */
.row-number {
    background: var(--accent-color) !important;
    color: #333 !important;
    font-weight: bold;
    width: 50px;
    min-width: 50px;
}

/* Notes Cards */
.notes-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.notes-header {
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
    border-bottom: 1px solid var(--border-color);
}

.notes-textarea {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    resize: vertical;
    min-height: 150px;
}

.notes-textarea:focus {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--accent-color);
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

/* Signatures Section */
.signatures-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.signatures-header {
    background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
    border-bottom: 1px solid var(--border-color);
}

.signature-box {
    text-align: center;
    padding: 1rem;
}

.signature-label {
    font-weight: bold;
    color: var(--text-secondary);
    margin-bottom: 1rem;
    display: block;
}

.signature-field {
    min-height: 60px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 0.5rem;
    margin-bottom: 1rem;
    outline: none;
    transition: all 0.3s ease;
}

.signature-field:focus {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--accent-color);
}

.signature-line {
    height: 2px;
    background: var(--border-color);
    margin-top: 1rem;
}

/* Print Styles */
@media print {
    .btn, .modal, .navbar, .sidebar, .column-controls {
        display: none !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
        page-break-inside: avoid;
    }

    .receipt-table {
        font-size: 10px;
    }

    .receipt-table th,
    .receipt-table td {
        padding: 4px 2px;
        border: 1px solid #000 !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .form-control, .table-input, .table-select, .notes-textarea, .signature-field {
        border: none !important;
        background: transparent !important;
        color: black !important;
    }

    .logo-circle {
        background: #ffd700 !important;
    }

    .receipt-title {
        color: #333 !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .receipt-table {
        font-size: 10px;
    }

    .column-header {
        min-width: 80px;
    }

    .table-input, .table-select {
        font-size: 10px;
        padding: 2px 4px;
    }

    .signature-box {
        margin-bottom: 1rem;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/receipts.js') }}"></script>
{% endblock %}
