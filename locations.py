from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, current_app, send_file
from flask_login import login_required, current_user
from datetime import datetime
from db import db
from models import Location, LocationEquipment, LocationPersonnel, Personnel
from sqlalchemy import or_
from werkzeug.utils import secure_filename
import json
import os
import uuid

locations_bp = Blueprint('locations', __name__, url_prefix='/locations')

# Configuration for file uploads
UPLOAD_FOLDER = 'static/uploads/location_instructions'
ALLOWED_EXTENSIONS = {'pdf'}
MAX_FILE_SIZE = 25 * 1024 * 1024  # 25 MB

def allowed_file(filename):
    """Check if the uploaded file has an allowed extension"""
    if not filename or '.' not in filename:
        return False

    file_extension = filename.rsplit('.', 1)[1].lower()
    return file_extension in ALLOWED_EXTENSIONS

def save_location_instructions(file, location_id):
    """Save uploaded location instructions PDF and return the file path"""
    if file and file.filename and allowed_file(file.filename):
        # Check file size
        file.seek(0, 2)  # Seek to end of file
        file_size = file.tell()
        file.seek(0)  # Reset to beginning

        if file_size == 0:
            print("File is empty")
            return None

        if file_size > MAX_FILE_SIZE:
            print(f"File too large: {file_size} bytes")
            return None

        # Create upload directory if it doesn't exist
        upload_dir = os.path.join(current_app.root_path, UPLOAD_FOLDER)
        os.makedirs(upload_dir, exist_ok=True)

        # Generate secure filename
        filename = secure_filename(file.filename)

        # Check if filename has extension
        if '.' not in filename:
            return None

        file_extension = filename.rsplit('.', 1)[1].lower()

        # Validate file extension
        if file_extension not in ALLOWED_EXTENSIONS:
            return None

        new_filename = f"location_{location_id}_instructions_{uuid.uuid4().hex[:8]}.{file_extension}"

        file_path = os.path.join(upload_dir, new_filename)

        try:
            file.save(file_path)
            # Return relative path for database storage
            return os.path.join(UPLOAD_FOLDER, new_filename).replace('\\', '/')
        except Exception as e:
            print(f"Error saving file: {e}")
            return None
    return None

def init_sample_locations():
    """إضافة مواقع تجريبية إذا لم تكن موجودة"""
    try:
        if Location.query.count() == 0:
            sample_locations = [
            {
                'name': 'البوابة الرئيسية',
                'serial_number': 'LOC001',
                'type': 'أمني',
                'status': 'نشط',
                'description': 'البوابة الرئيسية للمنشأة'
            },
            {
                'name': 'المستودع الأول',
                'serial_number': 'LOC002',
                'type': 'مستودع',
                'status': 'نشط',
                'description': 'مستودع الأسلحة والمعدات'
            },
            {
                'name': 'المستودع الثاني',
                'serial_number': 'LOC003',
                'type': 'مستودع',
                'status': 'نشط',
                'description': 'مستودع الذخيرة'
            },
            {
                'name': 'مكتب الحراسة',
                'serial_number': 'LOC004',
                'type': 'إداري',
                'status': 'نشط',
                'description': 'مكتب ضابط الحراسة'
            },
            {
                'name': 'نقطة التفتيش الأولى',
                'serial_number': 'LOC005',
                'type': 'أمني',
                'status': 'نشط',
                'description': 'نقطة تفتيش المركبات'
            },
            {
                'name': 'نقطة التفتيش الثانية',
                'serial_number': 'LOC006',
                'type': 'أمني',
                'status': 'نشط',
                'description': 'نقطة تفتيش الأفراد'
            },
            {
                'name': 'المخزن العام',
                'serial_number': 'LOC007',
                'type': 'مستودع',
                'status': 'نشط',
                'description': 'مخزن المواد العامة'
            },
            {
                'name': 'غرفة العمليات',
                'serial_number': 'LOC008',
                'type': 'إداري',
                'status': 'نشط',
                'description': 'غرفة العمليات والمراقبة'
            }
            ]

            # الحصول على أول مستخدم موجود
            from models import User
            first_user = User.query.first()
            user_id = first_user.id if first_user else None

            for loc_data in sample_locations:
                location = Location(**loc_data, created_by=user_id)
                db.session.add(location)
        
            try:
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                print(f"خطأ في إضافة المواقع التجريبية: {e}")
    except Exception as e:
        print(f"خطأ في تهيئة المواقع: {e}")

# دوال مساعدة للمواقع
def get_all_locations():
    """الحصول على جميع المواقع"""
    return Location.query.order_by(Location.name).all()

def get_location_by_id(location_id):
    """الحصول على موقع بالمعرف"""
    return Location.query.get_or_404(location_id)

def search_locations(search_term=None, location_type=None, status=None):
    """البحث في المواقع"""
    query = Location.query
    
    if search_term:
        query = query.filter(
            or_(
                Location.name.ilike(f'%{search_term}%'),
                Location.serial_number.ilike(f'%{search_term}%'),
                Location.description.ilike(f'%{search_term}%')
            )
        )
    
    if location_type:
        query = query.filter(Location.type == location_type)
    
    if status:
        query = query.filter(Location.status == status)
    
    return query.order_by(Location.name).all()

def create_new_location(data, files=None):
    """إنشاء موقع جديد"""
    location = Location(
        name=data.get('name'),
        serial_number=data.get('serial_number'),
        type=data.get('type', 'أمني'),
        status=data.get('status', 'نشط'),
        coordinates=data.get('coordinates'),
        description=data.get('description'),
        created_by=current_user.id if current_user.is_authenticated else 1
    )

    db.session.add(location)
    db.session.commit()

    # Handle instructions file upload after location is created
    if files and 'instructions_file' in files:
        instructions_file = files['instructions_file']
        if instructions_file and instructions_file.filename:
            instructions_path = save_location_instructions(instructions_file, location.id)
            if instructions_path:
                location.instructions_file = instructions_path
                db.session.commit()

    return location

def update_location(location_id, data, files=None):
    """تحديث موقع"""
    location = get_location_by_id(location_id)

    location.name = data.get('name', location.name)
    location.serial_number = data.get('serial_number', location.serial_number)
    location.type = data.get('type', location.type)
    location.status = data.get('status', location.status)
    location.coordinates = data.get('coordinates', location.coordinates)
    location.description = data.get('description', location.description)

    # Handle instructions file upload
    if files and 'instructions_file' in files:
        instructions_file = files['instructions_file']
        if instructions_file and instructions_file.filename:
            instructions_path = save_location_instructions(instructions_file, location_id)
            if instructions_path:
                # Remove old file if exists
                if location.instructions_file:
                    old_file_path = os.path.join(current_app.root_path, location.instructions_file)
                    if os.path.exists(old_file_path):
                        try:
                            os.remove(old_file_path)
                        except Exception as e:
                            print(f"Error removing old file: {e}")

                location.instructions_file = instructions_path
            else:
                print("Failed to save instructions file")

    # Handle file removal
    if data.get('remove_instructions_file') == 'true':
        if location.instructions_file:
            old_file_path = os.path.join(current_app.root_path, location.instructions_file)
            if os.path.exists(old_file_path):
                try:
                    os.remove(old_file_path)
                except Exception as e:
                    print(f"Error removing file: {e}")
            location.instructions_file = None

    db.session.commit()
    return location

def delete_location(location_id):
    """حذف موقع"""
    location = get_location_by_id(location_id)
    db.session.delete(location)
    db.session.commit()

def get_location_personnel(location_id):
    """الحصول على الأفراد المعينين لموقع"""
    return LocationPersonnel.query.filter_by(
        location_id=location_id, 
        is_active=True
    ).join(Personnel).all()

def assign_personnel_to_location(location_id, personnel_id, notes=None):
    """تعيين فرد لموقع"""
    existing = LocationPersonnel.query.filter_by(
        location_id=location_id,
        personnel_id=personnel_id,
        is_active=True
    ).first()
    
    if existing:
        return None, "الفرد معين بالفعل لهذا الموقع"
    
    assignment = LocationPersonnel(
        location_id=location_id,
        personnel_id=personnel_id,
        notes=notes
    )
    
    db.session.add(assignment)
    db.session.commit()
    return assignment, "تم تعيين الفرد بنجاح"

def remove_personnel_from_location(location_id, personnel_id):
    """إزالة فرد من موقع"""
    assignment = LocationPersonnel.query.filter_by(
        location_id=location_id,
        personnel_id=personnel_id,
        is_active=True
    ).first()
    
    if assignment:
        assignment.is_active = False
        assignment.end_date = datetime.now()
        db.session.commit()
        return True
    return False

# Routes
@locations_bp.route('/')
@login_required
def index():
    """صفحة إدارة المواقع الرئيسية"""
    init_sample_locations()
    locations = get_all_locations()
    return render_template('locations/index.html', locations=locations)

@locations_bp.route('/add')
@login_required
def add_location():
    """صفحة إضافة موقع جديد"""
    return render_template('locations/add.html')

@locations_bp.route('/create', methods=['POST'])
@login_required
def create_location():
    """إنشاء موقع جديد"""
    try:
        # Handle both JSON and form data
        if request.is_json:
            data = request.get_json()
            files = None
        else:
            data = request.form
            files = request.files

        if not data.get('name') or not data.get('serial_number'):
            return jsonify({'success': False, 'message': 'الاسم والرقم التسلسلي مطلوبان'})

        existing = Location.query.filter_by(serial_number=data.get('serial_number')).first()
        if existing:
            return jsonify({'success': False, 'message': 'الرقم التسلسلي موجود بالفعل'})

        location = create_new_location(data, files)
        
        if request.is_json:
            return jsonify({
                'success': True, 
                'message': 'تم إضافة الموقع بنجاح',
                'location': location.to_dict()
            })
        else:
            flash('تم إضافة الموقع بنجاح', 'success')
            return redirect(url_for('locations.index'))
            
    except Exception as e:
        db.session.rollback()
        if request.is_json:
            return jsonify({'success': False, 'message': f'خطأ في إضافة الموقع: {str(e)}'})
        else:
            flash(f'خطأ في إضافة الموقع: {str(e)}', 'error')
            return redirect(url_for('locations.add_location'))

@locations_bp.route('/<int:location_id>')
@login_required
def view_location(location_id):
    """عرض تفاصيل موقع محدد"""
    location = get_location_by_id(location_id)
    return render_template('locations/view.html', location=location)

@locations_bp.route('/<int:location_id>/delete', methods=['POST'])
@login_required
def delete_location_api(location_id):
    """حذف موقع محدد"""
    try:
        delete_location(location_id)
        return jsonify({'success': True, 'message': 'تم حذف الموقع بنجاح'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في حذف الموقع: {str(e)}'})

@locations_bp.route('/<int:location_id>/edit')
@login_required
def edit_location(location_id):
    """صفحة تعديل موقع محدد"""
    location = get_location_by_id(location_id)
    return render_template('locations/edit.html', location=location)

@locations_bp.route('/<int:location_id>/update', methods=['POST'])
@login_required
def update_location_api(location_id):
    """تحديث موقع محدد"""
    try:
        # Handle both JSON and form data
        if request.is_json:
            data = request.get_json()
            files = None
        else:
            data = request.form
            files = request.files

        location = update_location(location_id, data, files)

        if request.is_json:
            return jsonify({
                'success': True,
                'message': 'تم تحديث الموقع بنجاح',
                'location': location.to_dict()
            })
        else:
            return jsonify({
                'success': True,
                'message': 'تم تحديث الموقع بنجاح',
                'location': location.to_dict()
            })

    except Exception as e:
        db.session.rollback()
        if request.is_json:
            return jsonify({'success': False, 'message': f'خطأ في تحديث الموقع: {str(e)}'})
        else:
            flash(f'خطأ في تحديث الموقع: {str(e)}', 'error')
            return redirect(url_for('locations.edit_location', location_id=location_id))

@locations_bp.route('/<int:location_id>/personnel/search')
@login_required
def search_personnel_for_location(location_id):
    """البحث عن الأفراد لإضافتهم للموقع"""
    try:
        search_term = request.args.get('search_term', '').strip()
        if not search_term:
            return jsonify({'success': False, 'message': 'مصطلح البحث مطلوب'})

        # البحث عن الأفراد بالرقم العسكري أو الهوية الوطنية أو الاسم
        personnel_list = []

        if current_user.is_admin_role or current_user.is_company_duty:
            # مدير النظام ومناوب السرية يمكنهم البحث في جميع الأفراد
            personnel_list = Personnel.query.filter(
                or_(
                    Personnel.personnel_id.ilike(f'%{search_term}%'),
                    Personnel.phone.ilike(f'%{search_term}%'),
                    Personnel.name.ilike(f'%{search_term}%')
                )
            ).all()
        else:
            # للأدوار الأخرى، البحث في المستودعات المخصصة فقط
            warehouse_ids = [w.id for w in current_user.warehouses]
            if warehouse_ids:
                personnel_list = Personnel.query.filter(
                    Personnel.warehouse_id.in_(warehouse_ids),
                    or_(
                        Personnel.personnel_id.ilike(f'%{search_term}%'),
                        Personnel.phone.ilike(f'%{search_term}%'),
                        Personnel.name.ilike(f'%{search_term}%')
                    )
                ).all()

        if personnel_list:
            personnel_data = []
            for person in personnel_list:
                # التحقق من أن الفرد غير معين بالفعل لهذا الموقع
                existing_assignment = LocationPersonnel.query.filter_by(
                    location_id=location_id,
                    personnel_id=person.id,
                    is_active=True
                ).first()

                personnel_data.append({
                    'id': person.id,
                    'personnel_id': person.personnel_id,
                    'name': person.name,
                    'rank': person.rank,
                    'phone': person.phone,
                    'warehouse_name': person.warehouse.name if person.warehouse else 'غير محدد',
                    'already_assigned': existing_assignment is not None
                })

            return jsonify({
                'success': True,
                'personnel': personnel_data
            })
        else:
            return jsonify({
                'success': False,
                'message': 'لم يتم العثور على أفراد بهذا المصطلح'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في البحث: {str(e)}'
        })

@locations_bp.route('/<int:location_id>/personnel/add', methods=['POST'])
@login_required
def add_personnel_to_location(location_id):
    """إضافة فرد للموقع"""
    try:
        data = request.get_json()
        personnel_id = data.get('personnel_id')
        notes = data.get('notes', '')

        if not personnel_id:
            return jsonify({'success': False, 'message': 'معرف الفرد مطلوب'})

        # التحقق من وجود الفرد
        personnel = Personnel.query.get(personnel_id)
        if not personnel:
            return jsonify({'success': False, 'message': 'الفرد غير موجود'})

        # التحقق من وجود الموقع
        location = Location.query.get(location_id)
        if not location:
            return jsonify({'success': False, 'message': 'الموقع غير موجود'})

        # تعيين الفرد للموقع
        assignment, message = assign_personnel_to_location(location_id, personnel_id, notes)

        if assignment:
            return jsonify({
                'success': True,
                'message': message,
                'assignment': {
                    'id': assignment.id,
                    'personnel_name': personnel.name,
                    'personnel_rank': personnel.rank,
                    'assignment_date': assignment.assignment_date.strftime('%Y-%m-%d') if assignment.assignment_date else None
                }
            })
        else:
            return jsonify({'success': False, 'message': message})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'خطأ في إضافة الفرد: {str(e)}'})

@locations_bp.route('/<int:location_id>/personnel/<int:personnel_id>/remove', methods=['POST'])
@login_required
def remove_personnel_from_location_api(location_id, personnel_id):
    """إزالة فرد من الموقع"""
    try:
        success = remove_personnel_from_location(location_id, personnel_id)

        if success:
            return jsonify({
                'success': True,
                'message': 'تم إزالة الفرد من الموقع بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'الفرد غير معين لهذا الموقع'
            })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'خطأ في إزالة الفرد: {str(e)}'})

@locations_bp.route('/<int:location_id>/personnel/list')
@login_required
def get_location_personnel_list(location_id):
    """الحصول على قائمة الأفراد المعينين للموقع"""
    try:
        location = get_location_by_id(location_id)
        personnel_assignments = LocationPersonnel.query.filter_by(
            location_id=location_id,
            is_active=True
        ).join(Personnel).all()

        personnel_data = []
        for assignment in personnel_assignments:
            person = assignment.personnel
            if person:
                personnel_data.append({
                    'id': person.id,
                    'personnel_id': person.personnel_id,
                    'name': person.name,
                    'rank': person.rank,
                    'phone': person.phone,
                    'warehouse_name': person.warehouse.name if person.warehouse else 'غير محدد',
                    'assignment_notes': assignment.notes,
                    'assignment_date': assignment.assignment_date.strftime('%Y-%m-%d') if assignment.assignment_date else None
                })

        return jsonify({
            'success': True,
            'personnel': personnel_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب قائمة الأفراد: {str(e)}'
        })

@locations_bp.route('/api/list')
@login_required
def api_list_locations():
    """API لجلب قائمة المواقع"""
    try:
        locations = get_all_locations()
        return jsonify({
            'success': True,
            'locations': [location.to_dict() for location in locations]
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في جلب المواقع: {str(e)}',
            'locations': []
        })

@locations_bp.route('/<int:location_id>/download_instructions')
@login_required
def download_instructions(location_id):
    """تحميل ملف تعليمات الموقع"""
    try:
        location = get_location_by_id(location_id)

        if not location.instructions_file:
            flash('لا يوجد ملف تعليمات لهذا الموقع', 'warning')
            return redirect(url_for('locations.view_location', location_id=location_id))

        file_path = os.path.join(current_app.root_path, location.instructions_file)

        if not os.path.exists(file_path):
            flash('ملف التعليمات غير موجود', 'error')
            return redirect(url_for('locations.view_location', location_id=location_id))

        return send_file(file_path, as_attachment=True, download_name=f"تعليمات_الموقع_{location.name}.pdf")

    except Exception as e:
        flash(f'خطأ في تحميل الملف: {str(e)}', 'error')
        return redirect(url_for('locations.view_location', location_id=location_id))
