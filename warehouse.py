from datetime import datetime, timezone
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SubmitField, SelectField
from wtforms.validators import DataRequired, Length

from db import db
from models import Warehouse, Weapon, Personnel, Device, ActivityLog, WeaponTransaction, Audit, AuditItem
from utils import format_datetime_12h, format_time_12h

# Create the blueprint
warehouse_bp = Blueprint('warehouse', __name__, url_prefix='')

# Forms
class WarehouseForm(FlaskForm):
    name = StringField('اسم المستودع', validators=[DataRequired(), Length(min=2, max=100)])
    description = TextAreaField('الوصف')
    location = StringField('الموقع', validators=[Length(max=200)])
    submit = SubmitField('حفظ')

# Routes
@warehouse_bp.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('warehouse.dashboard'))
    return redirect(url_for('auth.login'))

@warehouse_bp.route('/dashboard')
@login_required
def dashboard():
    # منع مناوب السرية من الوصول للوحة التحكم
    if current_user.is_company_duty:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('receipts.index'))
    # Get the warehouses accessible to the current user
    # Si el usuario es administrador, mostrar todos los almacenes
    if current_user.is_admin_role:
        user_warehouses = Warehouse.query.all()
    else:
        user_warehouses = current_user.warehouses

    # Get the warehouse IDs the user has access to
    warehouse_ids = [w.id for w in user_warehouses]

    # Calculate total weapons and personnel across all warehouses
    total_weapons = Weapon.query.filter(Weapon.warehouse_id.in_(warehouse_ids)).count()
    total_personnel = Personnel.query.filter(Personnel.warehouse_id.in_(warehouse_ids)).count()
    # لا نحسب الأجهزة في الإحصائيات العامة لأنها أصبحت مستقلة عن المستودعات
    total_devices = 0

    # Get recent activities across all warehouses (last 7)
    recent_activities = ActivityLog.query.filter(
        ActivityLog.warehouse_id.in_(warehouse_ids)
    ).order_by(ActivityLog.id.desc()).limit(7).all()

    # Initialize the stats dictionary
    warehouse_stats = {}

    # Populate stats for each warehouse
    for warehouse in user_warehouses:
        weapons_count = Weapon.query.filter_by(warehouse_id=warehouse.id).count()
        personnel_count = Personnel.query.filter_by(warehouse_id=warehouse.id).count()
        devices_count = 0  # تم تعيين عدد الأجهزة إلى صفر لأنها أصبحت مستقلة عن المستودعات

        # Get weapon status counts
        active_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='نشط').count()
        maintenance_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='صيانة').count()
        leave_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='إجازة').count()
        mission_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='مهمة').count()
        cycle_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='دورة').count()
        vacant_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='شاغر').count()
        recipient_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='مستلم').count()
        shooting_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='رماية').count()
        other_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='أخرى').count()

        # Get personnel status counts
        active_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='نشط').count()
        leave_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='إجازة').count()
        mission_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='مهمة').count()
        cycle_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='دورة').count()
        recipient_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='مستلم').count()
        shooting_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='رماية').count()

        # Get recent activity for this warehouse
        recent_activity = ActivityLog.query.filter_by(warehouse_id=warehouse.id).order_by(ActivityLog.id.desc()).limit(5).all()

        # Get recent weapon transactions for this warehouse
        recent_transactions = WeaponTransaction.query.filter_by(source_warehouse_id=warehouse.id).order_by(WeaponTransaction.timestamp.desc()).limit(5).all()

        # Store stats in dictionary
        warehouse_stats[warehouse.id] = {
            'id': warehouse.id,
            'name': warehouse.name,
            'weapons_count': weapons_count,
            'personnel_count': personnel_count,
            'devices_count': 0,  # تم تعيين عدد الأجهزة إلى صفر لأنها أصبحت مستقلة عن المستودعات
            'weapon_status': {
                'active': active_weapons,
                'maintenance': maintenance_weapons,
                'leave': leave_weapons,
                'mission': mission_weapons,
                'cycle': cycle_weapons,
                'vacant': vacant_weapons,
                'recipient': recipient_weapons,
                'shooting': shooting_weapons,
                'other': other_weapons
            },
            'personnel_status': {
                'active': active_personnel,
                'leave': leave_personnel,
                'mission': mission_personnel,
                'cycle': cycle_personnel,
                'recipient': recipient_personnel,
                'shooting': shooting_personnel
            },
            'recent_activity': recent_activity,
            'recent_transactions': recent_transactions
        }

    # إنشاء بيانات المقارنة للرسم البياني (بدون الأجهزة)
    warehouse_comparison_data = {
        'labels': [w.name for w in user_warehouses],
        'datasets': [
            {
                'label': 'الأسلحة',
                'data': [warehouse_stats[w.id]['weapons_count'] for w in user_warehouses],
                'backgroundColor': 'rgba(0, 123, 255, 0.7)'
            },
            {
                'label': 'الأفراد',
                'data': [warehouse_stats[w.id]['personnel_count'] for w in user_warehouses],
                'backgroundColor': 'rgba(40, 167, 69, 0.7)'
            }
        ]
    }

    return render_template('dashboard.html',
                          warehouse_stats=warehouse_stats,
                          warehouses=user_warehouses,
                          warehouse_comparison_data=warehouse_comparison_data,
                          total_weapons=total_weapons,
                          total_personnel=total_personnel,
                          total_devices=total_devices,
                          recent_activities=recent_activities,
                          format_datetime_12h=format_datetime_12h,
                          format_time_12h=format_time_12h,
                          title='لوحة التحكم')

@warehouse_bp.route('/warehouse/<int:warehouse_id>')
@login_required
def warehouse_detail(warehouse_id):
    # Check if user has access to this warehouse
    warehouse = Warehouse.query.get_or_404(warehouse_id)
    if not current_user.is_admin_role and warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('warehouse.dashboard'))

    # Get warehouse statistics
    weapons_count = Weapon.query.filter_by(warehouse_id=warehouse_id).count()
    personnel_count = Personnel.query.filter_by(warehouse_id=warehouse_id).count()
    devices_count = 0  # تم تعيين عدد الأجهزة إلى صفر لأنها أصبحت مستقلة عن المستودعات

    # Get weapon status counts
    active_weapons = Weapon.query.filter_by(warehouse_id=warehouse_id, status='نشط').count()
    maintenance_weapons = Weapon.query.filter_by(warehouse_id=warehouse_id, status='صيانة').count()
    leave_weapons = Weapon.query.filter_by(warehouse_id=warehouse_id, status='إجازة').count()
    mission_weapons = Weapon.query.filter_by(warehouse_id=warehouse_id, status='مهمة').count()
    cycle_weapons = Weapon.query.filter_by(warehouse_id=warehouse_id, status='دورة').count()
    vacant_weapons = Weapon.query.filter_by(warehouse_id=warehouse_id, status='شاغر').count()
    other_weapons = Weapon.query.filter_by(warehouse_id=warehouse_id, status='أخرى').count()

    # Get personnel status counts
    active_personnel = Personnel.query.filter_by(warehouse_id=warehouse_id, status='نشط').count()
    leave_personnel = Personnel.query.filter_by(warehouse_id=warehouse_id, status='إجازة').count()
    mission_personnel = Personnel.query.filter_by(warehouse_id=warehouse_id, status='مهمة').count()
    cycle_personnel = Personnel.query.filter_by(warehouse_id=warehouse_id, status='دورة').count()
    stoped_personnel = Personnel.query.filter_by(warehouse_id=warehouse_id, status='مستلم').count()
    shooting_personnel = Personnel.query.filter_by(warehouse_id=warehouse_id, status='رماية').count()

    # Get recent activity
    recent_activity = ActivityLog.query.filter_by(warehouse_id=warehouse_id).order_by(ActivityLog.id.desc()).limit(10).all()

    # Get recent transactions
    recent_transactions = WeaponTransaction.query.filter_by(source_warehouse_id=warehouse_id).order_by(WeaponTransaction.timestamp.desc()).limit(10).all()

    return render_template('warehouse/details.html',
                          warehouse=warehouse,
                          weapons_count=weapons_count,
                          personnel_count=personnel_count,
                          devices_count=devices_count,
                          weapon_status={
                              'active': active_weapons,
                              'maintenance': maintenance_weapons,
                              'leave': leave_weapons,
                              'mission': mission_weapons,
                              'cycle': cycle_weapons,
                              'vacant': vacant_weapons,
                              'recipient': Weapon.query.filter_by(warehouse_id=warehouse_id, status='مستلم').count(),
                              'shooting': Weapon.query.filter_by(warehouse_id=warehouse_id, status='رماية').count(),
                              'other': other_weapons
                          },
                          personnel_status={
                              'active': active_personnel,
                              'leave': leave_personnel,
                              'mission': mission_personnel,
                              'cycle': cycle_personnel,
                              'recipient': stoped_personnel,
                              'shooting': shooting_personnel
                          },
                          recent_activity=recent_activity,
                          recent_transactions=recent_transactions,
                          title=f'تفاصيل {warehouse.name}')



@warehouse_bp.route('/warehouse/create', methods=['GET', 'POST'])
@login_required
def create_warehouse():
    # Only admins and warehouse managers can create warehouses
    if not (current_user.is_admin_role or current_user.is_warehouse_manager):
        flash('ليس لديك صلاحية لإنشاء مستودعات جديدة', 'danger')
        return redirect(url_for('warehouse.dashboard'))

    form = WarehouseForm()
    if form.validate_on_submit():
        warehouse = Warehouse(
            name=form.name.data,
            description=form.description.data,
            location=form.location.data
        )
        db.session.add(warehouse)
        db.session.flush()  # This assigns an ID without committing

        # Log the warehouse creation
        log = ActivityLog(
            action="إنشاء مستودع",
            description=f"تم إنشاء مستودع جديد: {warehouse.name}",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=warehouse.id
        )
        db.session.add(log)
        db.session.commit()
        flash('تم إنشاء المستودع بنجاح!', 'success')
        return redirect(url_for('warehouse.manage'))

    return render_template('warehouse/create.html', form=form, title='إنشاء مستودع جديد')

@warehouse_bp.route('/warehouse/<int:warehouse_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_warehouse(warehouse_id):
    # Only admins and warehouse managers can edit warehouses
    if not (current_user.is_admin_role or current_user.is_warehouse_manager):
        flash('ليس لديك صلاحية لتعديل المستودعات', 'danger')
        return redirect(url_for('warehouse.dashboard'))

    warehouse = Warehouse.query.get_or_404(warehouse_id)
    form = WarehouseForm(obj=warehouse)

    if form.validate_on_submit():
        warehouse.name = form.name.data
        warehouse.description = form.description.data
        warehouse.location = form.location.data

        # Log the warehouse update
        log = ActivityLog(
            action="تعديل مستودع",
            description=f"تم تعديل بيانات المستودع: {warehouse.name}",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=warehouse.id
        )
        db.session.add(log)

        db.session.commit()
        flash('تم تحديث بيانات المستودع بنجاح!', 'success')
        return redirect(url_for('warehouse.manage'))

    return render_template('warehouse/edit.html', form=form, warehouse=warehouse, title='تعديل مستودع')


@warehouse_bp.route('/warehouse/<int:warehouse_id>/delete', methods=['POST'])
@login_required
def delete_warehouse(warehouse_id):
    """Delete a warehouse - admin and warehouse manager only"""
    # Only admins and warehouse managers can delete warehouses
    if not (current_user.is_admin_role or current_user.is_warehouse_manager):
        flash('ليس لديك صلاحية لحذف المستودعات', 'danger')
        return redirect(url_for('warehouse.manage'))

    warehouse = Warehouse.query.get_or_404(warehouse_id)

    # Check if warehouse has any related data
    weapons_count = warehouse.weapons.count()
    personnel_count = warehouse.personnel.count()
    devices_count = warehouse.devices.count()

    if weapons_count > 0 or personnel_count > 0 or devices_count > 0:
        flash(f'لا يمكن حذف المستودع "{warehouse.name}" لأنه يحتوي على بيانات مرتبطة: {weapons_count} أسلحة، {personnel_count} أفراد، {devices_count} أجهزة. يجب نقل أو حذف هذه البيانات أولاً.', 'danger')
        return redirect(url_for('warehouse.manage'))

    try:
        warehouse_name = warehouse.name

        # Delete related activity logs first
        ActivityLog.query.filter_by(warehouse_id=warehouse_id).delete()

        # Delete related audits and audit items
        audit_ids = [audit.id for audit in Audit.query.filter_by(warehouse_id=warehouse_id).all()]
        if audit_ids:
            AuditItem.query.filter(AuditItem.audit_id.in_(audit_ids)).delete(synchronize_session=False)
        Audit.query.filter_by(warehouse_id=warehouse_id).delete()

        # Delete weapon transactions related to this warehouse
        WeaponTransaction.query.filter(
            (WeaponTransaction.source_warehouse_id == warehouse_id) |
            (WeaponTransaction.target_warehouse_id == warehouse_id)
        ).delete(synchronize_session=False)

        # Remove warehouse from users' associations
        for user in warehouse.users:
            user.warehouses.remove(warehouse)

        # Delete the warehouse
        db.session.delete(warehouse)

        # Log the warehouse deletion
        log = ActivityLog(
            action="حذف مستودع",
            description=f"تم حذف المستودع: {warehouse_name}",
            ip_address=request.remote_addr,
            user_id=current_user.id
        )
        db.session.add(log)

        db.session.commit()
        flash(f'تم حذف المستودع "{warehouse_name}" بنجاح!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المستودع: {str(e)}', 'danger')

    return redirect(url_for('warehouse.manage'))

@warehouse_bp.route('/display/warehouse1')
def display_warehouse1():
    # This route is for the dedicated display screen for Warehouse 1
    warehouse = Warehouse.query.filter_by(name='المستودع الأول').first_or_404()

    # Get warehouse statistics
    weapons_count = Weapon.query.filter_by(warehouse_id=warehouse.id).count()
    personnel_count = Personnel.query.filter_by(warehouse_id=warehouse.id).count()
    # لا نحسب الأجهزة في إحصائيات المستودعات لأنها أصبحت مستقلة

    # Get personnel status counts
    active_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='نشط').count()
    leave_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='إجازة').count()
    mission_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='مهمة').count()
    cycle_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='دورة').count()
    stoped_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='مستلم').count()

    # Get weapon status counts
    active_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='نشط').count()
    maintenance_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='صيانة').count()
    leave_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='إجازة').count()
    mission_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='مهمة').count()
    cycle_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='دورة').count()
    vacant_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='شاغر').count()

    # Get recent transactions
    recent_transactions = WeaponTransaction.query.filter_by(source_warehouse_id=warehouse.id).order_by(WeaponTransaction.timestamp.desc()).limit(5).all()

    # Determine which template to use based on a query parameter
    template = request.args.get('template', 'modern')

    if template == 'custom':
        template_file = 'display/warehouse1_custom.html'
    elif template == 'modern':
        template_file = 'display/warehouse1_modern.html'
    else:
        template_file = 'display/warehouse1.html'

    return render_template(template_file,
                          warehouse=warehouse,
                          weapons_count=weapons_count,
                          personnel_count=personnel_count,
                          weapon_status={
                              'active': active_weapons,
                              'maintenance': maintenance_weapons,
                              'leave': leave_weapons,
                              'mission': mission_weapons,
                              'cycle': cycle_weapons,
                              'vacant': vacant_weapons,
                              'recipient': Weapon.query.filter_by(warehouse_id=warehouse.id, status='مستلم').count(),
                              'shooting': Weapon.query.filter_by(warehouse_id=warehouse.id, status='رماية').count()
                          },
                          active_personnel=active_personnel,
                          leave_personnel=leave_personnel,
                          mission_personnel=mission_personnel,
                          cycle_personnel=cycle_personnel,
                          stoped_personnel=stoped_personnel,
                          recent_transactions=recent_transactions,
                          title=f'شاشة عرض {warehouse.name}')

@warehouse_bp.route('/display/warehouse2')
def display_warehouse2():
    # This route is for the dedicated display screen for Warehouse 2
    warehouse = Warehouse.query.filter_by(name='المستودع الثاني').first_or_404()
    
    # Get warehouse statistics
    weapons_count = Weapon.query.filter_by(warehouse_id=warehouse.id).count()
    personnel_count = Personnel.query.filter_by(warehouse_id=warehouse.id).count()
    # لا نحسب الأجهزة في إحصائيات المستودعات لأنها أصبحت مستقلة

    # Get personnel status counts
    active_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='نشط').count()
    leave_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='إجازة').count()
    mission_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='مهمة').count()
    cycle_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='دورة').count()
    stoped_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='مستلم').count()

    # Get weapon status counts
    active_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='نشط').count()
    maintenance_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='صيانة').count()
    leave_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='إجازة').count()
    mission_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='مهمة').count()
    cycle_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='دورة').count()
    vacant_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='شاغر').count()

    # Get recent transactions
    recent_transactions = WeaponTransaction.query.filter_by(source_warehouse_id=warehouse.id).order_by(WeaponTransaction.timestamp.desc()).limit(5).all()

    # Determine which template to use based on a query parameter
    template = request.args.get('template', 'modern')

    if template == 'modern':
        template_file = 'display/warehouse2_modern.html'
    else:
        template_file = 'display/warehouse2.html'

    return render_template(template_file,
                          warehouse=warehouse,
                          weapons_count=weapons_count,
                          personnel_count=personnel_count,
                          weapon_status={
                              'active': active_weapons,
                              'maintenance': maintenance_weapons,
                              'leave': leave_weapons,
                              'mission': mission_weapons,
                              'cycle': cycle_weapons,
                              'vacant': vacant_weapons,
                              'recipient': Weapon.query.filter_by(warehouse_id=warehouse.id, status='مستلم').count(),
                              'shooting': Weapon.query.filter_by(warehouse_id=warehouse.id, status='رماية').count()
                          },
                          active_personnel=active_personnel,
                          leave_personnel=leave_personnel,
                          mission_personnel=mission_personnel,
                          cycle_personnel=cycle_personnel,
                          stoped_personnel=stoped_personnel,
                          recent_transactions=recent_transactions,
                          title=f'شاشة عرض {warehouse.name}')

@warehouse_bp.route('/display/storage')
def display_storage():
    # This route is for the dedicated display screen for General Storage
    warehouse = Warehouse.query.filter_by(name='المخزون العام').first_or_404()
    
    # Get warehouse statistics
    weapons_count = Weapon.query.filter_by(warehouse_id=warehouse.id).count()
    personnel_count = Personnel.query.filter_by(warehouse_id=warehouse.id).count()
    # لا نحسب الأجهزة في إحصائيات المستودعات لأنها أصبحت مستقلة

    # Get personnel status counts
    active_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='نشط').count()
    leave_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='إجازة').count()
    mission_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='مهمة').count()
    cycle_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='دورة').count()
    stoped_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='مستلم').count()

    # Get weapon status counts
    active_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='نشط').count()
    maintenance_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='صيانة').count()
    leave_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='إجازة').count()
    mission_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='مهمة').count()
    cycle_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='دورة').count()
    vacant_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='شاغر').count()

    # Get recent transactions
    recent_transactions = WeaponTransaction.query.filter_by(source_warehouse_id=warehouse.id).order_by(WeaponTransaction.timestamp.desc()).limit(5).all()

    # Determine which template to use based on a query parameter
    template = request.args.get('template', 'modern')

    if template == 'modern':
        template_file = 'display/storage_modern.html'
    else:
        template_file = 'display/storage.html'

    return render_template(template_file,
                          warehouse=warehouse,
                          weapons_count=weapons_count,
                          personnel_count=personnel_count,
                          weapon_status={
                              'active': active_weapons,
                              'maintenance': maintenance_weapons,
                              'leave': leave_weapons,
                              'mission': mission_weapons,
                              'cycle': cycle_weapons,
                              'vacant': vacant_weapons,
                              'recipient': Weapon.query.filter_by(warehouse_id=warehouse.id, status='مستلم').count(),
                              'shooting': Weapon.query.filter_by(warehouse_id=warehouse.id, status='رماية').count()
                          },
                          active_personnel=active_personnel,
                          leave_personnel=leave_personnel,
                          mission_personnel=mission_personnel,
                          cycle_personnel=cycle_personnel,
                          stoped_personnel=stoped_personnel,
                          recent_transactions=recent_transactions,
                          title=f'شاشة عرض {warehouse.name}')

@warehouse_bp.route('/warehouse/<int:warehouse_id>/display')
@login_required
def display(warehouse_id):
    """Display warehouse dashboard on a large screen"""
    # Check if user has access to this warehouse
    warehouse = Warehouse.query.get_or_404(warehouse_id)
    if not current_user.is_admin_role and warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول إلى هذا المستودع', 'danger')
        return redirect(url_for('warehouse.dashboard'))

    # Determine which display template to use based on warehouse name
    if warehouse.name == 'المستودع الأول':
        return redirect(url_for('warehouse.display_warehouse1'))
    elif warehouse.name == 'المستودع الثاني':
        return redirect(url_for('warehouse.display_warehouse2'))
    elif warehouse.name == 'المخزون العام':
        return redirect(url_for('warehouse.display_storage'))
    else:
        # For other warehouses, use a generic display
        # Get warehouse statistics
        weapons_count = Weapon.query.filter_by(warehouse_id=warehouse_id).count()
        personnel_count = Personnel.query.filter_by(warehouse_id=warehouse_id).count()
        devices_count = 0  # تم تعيين عدد الأجهزة إلى صفر لأنها أصبحت مستقلة عن المستودعات

        # Get weapon status counts
        active_weapons = Weapon.query.filter_by(warehouse_id=warehouse_id, status='نشط').count()
        maintenance_weapons = Weapon.query.filter_by(warehouse_id=warehouse_id, status='صيانة').count()
        leave_weapons = Weapon.query.filter_by(warehouse_id=warehouse_id, status='إجازة').count()
        mission_weapons = Weapon.query.filter_by(warehouse_id=warehouse_id, status='مهمة').count()
        cycle_weapons = Weapon.query.filter_by(warehouse_id=warehouse_id, status='دورة').count()
        vacant_weapons = Weapon.query.filter_by(warehouse_id=warehouse_id, status='شاغر').count()

        # Get personnel status counts
        active_personnel = Personnel.query.filter_by(warehouse_id=warehouse_id, status='نشط').count()
        leave_personnel = Personnel.query.filter_by(warehouse_id=warehouse_id, status='إجازة').count()
        mission_personnel = Personnel.query.filter_by(warehouse_id=warehouse_id, status='مهمة').count()

        # Get recent transactions
        recent_transactions = WeaponTransaction.query.filter_by(source_warehouse_id=warehouse_id).order_by(WeaponTransaction.timestamp.desc()).limit(10).all()

        # Use display/generic.html as a fallback if it exists, otherwise use storage.html
        try:
            return render_template('display/generic.html',
                            warehouse=warehouse,
                            weapons_count=weapons_count,
                            personnel_count=personnel_count,
                            devices_count=devices_count,
                            weapon_status={
                                'active': active_weapons,
                                'maintenance': maintenance_weapons,
                                'leave': leave_weapons,
                                'mission': mission_weapons,
                                'cycle': cycle_weapons,
                                'vacant': vacant_weapons,
                                'recipient': Weapon.query.filter_by(warehouse_id=warehouse_id, status='مستلم').count(),
                                'shooting': Weapon.query.filter_by(warehouse_id=warehouse_id, status='رماية').count()
                            },
                            personnel_status={
                                'active': active_personnel,
                                'leave': leave_personnel,
                                'mission': mission_personnel,
                                'cycle': Personnel.query.filter_by(warehouse_id=warehouse_id, status='دورة').count(),
                                'shooting': Personnel.query.filter_by(warehouse_id=warehouse_id, status='رماية').count()
                            },
                            recent_transactions=recent_transactions,
                            title=f'شاشة عرض {warehouse.name}')
        except:
            # Use modern template by default
            return render_template('display/storage_modern.html',
                            warehouse=warehouse,
                            weapons_count=weapons_count,
                            personnel_count=personnel_count,
                            devices_count=devices_count,
                            weapon_status={
                                'active': active_weapons,
                                'maintenance': maintenance_weapons,
                                'leave': leave_weapons,
                                'mission': mission_weapons,
                                'cycle': cycle_weapons,
                                'vacant': vacant_weapons,
                                'recipient': Weapon.query.filter_by(warehouse_id=warehouse_id, status='مستلم').count(),
                                'shooting': Weapon.query.filter_by(warehouse_id=warehouse_id, status='رماية').count()
                            },
                            personnel_status={
                                'active': active_personnel,
                                'leave': leave_personnel,
                                'mission': mission_personnel,
                                'cycle': Personnel.query.filter_by(warehouse_id=warehouse_id, status='دورة').count(),
                                'recipient': 0,
                                'shooting': Personnel.query.filter_by(warehouse_id=warehouse_id, status='رماية').count()
                            },
                            recent_transactions=recent_transactions,
                            title=f'شاشة عرض {warehouse.name}')

@warehouse_bp.route('/api/warehouse/<int:warehouse_id>/stats')
def warehouse_stats_api(warehouse_id):
    """API endpoint to get live warehouse statistics for the display screens"""
    warehouse = Warehouse.query.get_or_404(warehouse_id)

    # Verificar si el usuario tiene acceso a este almacén
    if current_user.is_authenticated and not current_user.is_admin_role and warehouse not in current_user.warehouses:
        return jsonify({'error': 'ليس لديك صلاحية للوصول إلى هذا المستودع'}), 403

    # Get warehouse statistics
    weapons_count = Weapon.query.filter_by(warehouse_id=warehouse.id).count()
    personnel_count = Personnel.query.filter_by(warehouse_id=warehouse.id).count()

    # Get weapon status counts
    active_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='نشط').count()
    maintenance_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='صيانة').count()
    leave_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='إجازة').count()
    mission_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='مهمة').count()
    cycle_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='دورة').count()
    vacant_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='شاغر').count()
    recipient_weapons = Weapon.query.filter_by(warehouse_id=warehouse.id, status='مستلم').count()

    # Get personnel status counts
    active_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='نشط').count()
    leave_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='إجازة').count()
    mission_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='مهمة').count()
    cycle_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='دورة').count()
    stoped_personnel = Personnel.query.filter_by(warehouse_id=warehouse.id, status='مستلم').count()

    # Get recent transactions
    recent_transactions = WeaponTransaction.query.filter_by(source_warehouse_id=warehouse.id).order_by(WeaponTransaction.timestamp.desc()).limit(5).all()

    # Format transactions for JSON response
    transactions_data = []
    for transaction in recent_transactions:
        transaction_data = {
            'id': transaction.id,
            'type': transaction.transaction_type,
            'timestamp': transaction.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'weapon_id': transaction.weapon_id,
            'personnel_id': transaction.personnel_id
        }

        # Add weapon and personnel details if available
        if transaction.weapon:
            transaction_data['weapon_name'] = transaction.weapon.name
            transaction_data['weapon_serial'] = transaction.weapon.serial_number

        if transaction.personnel:
            transaction_data['personnel_name'] = transaction.personnel.name
            transaction_data['personnel_id_number'] = transaction.personnel.personnel_id

        transactions_data.append(transaction_data)

    stats = {
        'warehouse_name': warehouse.name,
        'weapons_count': weapons_count,
        'personnel_count': personnel_count,
        'weapon_status': {
            'active': active_weapons,
            'maintenance': maintenance_weapons,
            'leave': leave_weapons,
            'mission': mission_weapons,
            'cycle': cycle_weapons,
            'vacant': vacant_weapons,
            'recipient': recipient_weapons,
            'shooting': Weapon.query.filter_by(warehouse_id=warehouse.id, status='رماية').count()
        },
        'personnel_status': {
            'active': active_personnel,
            'leave': leave_personnel,
            'mission': mission_personnel,
            'cycle': cycle_personnel,
            'recipient': stoped_personnel,
            'shooting': Personnel.query.filter_by(warehouse_id=warehouse.id, status='رماية').count()
        },
        'recent_transactions': transactions_data,
        'updated_at': datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')
    }

    return jsonify(stats)


@warehouse_bp.route('/manage-warehouses')
@login_required
def manage():
    """Manage warehouses - admin and warehouse manager only"""
    # Check if user is admin or warehouse manager
    if not (current_user.is_admin_role or current_user.is_warehouse_manager):
        flash('ليس لديك صلاحية للوصول إلى إدارة المستودعات', 'danger')
        return redirect(url_for('warehouse.dashboard'))

    # Get all warehouses
    warehouses = Warehouse.query.all()

    return render_template('warehouse/manage.html', warehouses=warehouses, title='إدارة المستودعات')


@warehouse_bp.route('/create-warehouse', methods=['GET', 'POST'])
@login_required
def create():
    """Create a new warehouse - admin and warehouse manager only"""
    # Check if user is admin or warehouse manager
    if not (current_user.is_admin_role or current_user.is_warehouse_manager):
        flash('ليس لديك صلاحية لإنشاء مستودع جديد', 'danger')
        return redirect(url_for('warehouse.dashboard'))

    form = WarehouseForm()
    if form.validate_on_submit():
        new_warehouse = Warehouse(
            name=form.name.data,
            description=form.description.data,
            location=form.location.data
        )
        db.session.add(new_warehouse)
        db.session.flush()  # This assigns an ID without committing

        # Log the warehouse creation
        log = ActivityLog(
            action="إنشاء مستودع",
            description=f"تم إنشاء مستودع جديد: {form.name.data}",
            ip_address=request.remote_addr,
            user_id=current_user.id,
            warehouse_id=new_warehouse.id
        )
        db.session.add(log)
        db.session.commit()
        flash('تم إنشاء المستودع الجديد بنجاح!', 'success')
        return redirect(url_for('warehouse.manage'))

    return render_template('warehouse/create.html', form=form, title='إنشاء مستودع جديد')
