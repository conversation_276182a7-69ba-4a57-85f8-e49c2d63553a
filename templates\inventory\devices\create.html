{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>إضافة جهاز جديد</h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى قائمة الأجهزة
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-desktop"></i> بيانات الجهاز</h5>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('inventory.create_device') }}">
            {{ form.hidden_tag() }}
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.name.label }}
                        {{ form.name(class="form-control") }}
                        {% if form.name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.name.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.type.label }}
                        {{ form.type(class="form-control") }}
                        {% if form.type.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.type.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.model.label }}
                        {{ form.model(class="form-control") }}
                        {% if form.model.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.model.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.serial_number.label }}
                        {{ form.serial_number(class="form-control") }}
                        {% if form.serial_number.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.serial_number.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.status.label }}
                        {{ form.status(class="form-control") }}
                        {% if form.status.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.status.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        {{ form.warehouse_id.label }}
                        {{ form.warehouse_id(class="form-control") }}
                        {% if form.warehouse_id.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.warehouse_id.errors %}
                            <span>{{ error }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="form-group">
                {{ form.notes.label }}
                {{ form.notes(class="form-control", rows=4) }}
                {% if form.notes.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.notes.errors %}
                    <span>{{ error }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            <div class="form-group mt-4">
                {{ form.submit(class="btn btn-primary") }}
                <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary ml-2">إلغاء</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}