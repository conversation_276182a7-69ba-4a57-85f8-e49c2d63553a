{% extends "base.html" %}

{% block title %}إدارة المواقع{% endblock %}

{% block styles %}
<style>
    /* تنسيق بطاقات الإحصائيات البسيطة */
    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
    }

    .text-gray-300 {
        color: #d1d3e2 !important;
    }

    .border-left-primary {
        border-left: 4px solid #4e73df !important;
    }

    .border-left-success {
        border-left: 4px solid #1cc88a !important;
    }

    .border-left-warning {
        border-left: 4px solid #f6c23e !important;
    }

    .border-left-info {
        border-left: 4px solid #36b9cc !important;
    }

    /* Grid Layout for Locations */
    .locations-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    /* Responsive Grid - 4 cards per row on large screens */
    @media (min-width: 1200px) {
        .locations-grid {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    /* 3 cards per row on medium screens */
    @media (min-width: 992px) and (max-width: 1199px) {
        .locations-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    /* 2 cards per row on small screens */
    @media (min-width: 768px) and (max-width: 991px) {
        .locations-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    /* 1 card per row on mobile */
    @media (max-width: 767px) {
        .locations-grid {
            grid-template-columns: 1fr;
        }
    }

    .location-card {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 20px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        height: fit-content;
        display: flex;
        flex-direction: column;
    }

    .location-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border-color: var(--primary-color);
    }

    .location-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 2px solid var(--border-color);
        position: relative;
    }

    .location-header::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 30%;
        height: 2px;
        background: linear-gradient(90deg, var(--primary-color), transparent);
    }

    .location-title {
        font-size: 1.1rem;
        font-weight: bold;
        color: var(--text-primary);
        margin: 0;
        line-height: 1.3;
    }

    .location-serial {
        font-size: 0.9rem;
        color: var(--text-secondary);
        margin: 5px 0 0 0;
    }

    .status-badge {
        padding: 6px 14px;
        border-radius: 25px;
        font-size: 0.8rem;
        font-weight: 600;
        text-align: center;
        min-width: 85px;
        box-shadow: 0 2px 6px rgba(0,0,0,0.1);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-active {
        background: linear-gradient(135deg, #d4edda, #c3e6cb);
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    .status-inactive {
        background: linear-gradient(135deg, #f8d7da, #f5c6cb);
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    .status-withdrawn {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    .status-maintenance {
        background: linear-gradient(135deg, #cce7ff, #b3d7ff);
        color: #004085;
        border: 1px solid #b3d7ff;
    }

    .location-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 15px;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .info-icon {
        width: 20px;
        text-align: center;
        color: var(--primary-color);
        font-size: 0.9rem;
        opacity: 0.8;
        transition: all 0.2s ease;
    }

    .info-item:hover .info-icon {
        opacity: 1;
        transform: scale(1.1);
    }

    .equipment-section {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid var(--border-color);
    }

    .equipment-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 10px;
    }

    .equipment-tag {
        background: linear-gradient(135deg, var(--primary-color), #0056b3);
        color: white;
        padding: 4px 10px;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        margin-top: auto;
        padding-top: 15px;
        justify-content: center;
    }

    .btn-action {
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.85rem;
        text-decoration: none;
        transition: all 0.2s ease;
        flex: 1;
        text-align: center;
        border: none;
        cursor: pointer;
    }

    .btn-view {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        box-shadow: 0 2px 4px rgba(0,123,255,0.3);
    }
    .btn-edit {
        background: linear-gradient(135deg, #28a745, #1e7e34);
        color: white;
        box-shadow: 0 2px 4px rgba(40,167,69,0.3);
    }
    .btn-delete {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        box-shadow: 0 2px 4px rgba(220,53,69,0.3);
    }

    .btn-action:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        color: white;
        text-decoration: none;
    }

    .search-filters {
        background: var(--card-bg);
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
        border: 1px solid var(--border-color);
    }

    .filter-row {
        display: grid;
        grid-template-columns: 1fr 200px 200px auto;
        gap: 15px;
        align-items: end;
    }

    .add-location-btn {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .add-location-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
        color: white;
        text-decoration: none;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: var(--text-secondary);
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    @media (max-width: 768px) {
        .filter-row {
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .location-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .action-buttons {
            justify-content: center;
            flex-direction: column;
            gap: 8px;
        }

        .btn-action {
            padding: 10px 16px;
            font-size: 0.9rem;
        }

        .location-info {
            grid-template-columns: 1fr;
            gap: 8px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="mb-2">إدارة المواقع</h1>
            <p class="text-muted mb-0">إدارة وتتبع جميع المواقع والعهد المخصصة لها</p>
        </div>
        <a href="{{ url_for('locations.add_location') }}" class="add-location-btn">
            <i class="fas fa-plus"></i>
            إضافة موقع جديد
        </a>
    </div>

    <!-- Search and Filters -->
    <div class="search-filters">
        <div class="filter-row">
            <div>
                <label class="form-label">البحث في المواقع</label>
                <input type="text" id="searchInput" class="form-control" placeholder="ابحث بالاسم أو الرقم التسلسلي...">
            </div>
            <div>
                <label class="form-label">نوع الموقع</label>
                <select id="typeFilter" class="form-select">
                    <option value="">جميع الأنواع</option>
                    <option value="أمني">أمني</option>
                    <option value="إداري">إداري</option>
                    <option value="خدمي">خدمي</option>
                    <option value="تقني">تقني</option>
                </select>
            </div>
            <div>
                <label class="form-label">حالة الموقع</label>
                <select id="statusFilter" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="نشط">نشط</option>
                    <option value="غير نشط">غير نشط</option>
                    <option value="مسحوب">مسحوب</option>
                    <option value="تحت الصيانة">تحت الصيانة</option>
                </select>
            </div>
            <div>
                <button type="button" id="clearFilters" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i>
                    مسح الفلاتر
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-left-primary shadow h-100 py-2" style="border-left: 4px solid #4e73df;">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0 text-primary" id="totalLocations">{{ locations|length }}</h4>
                            <p class="mb-0 text-muted">إجمالي المواقع</p>
                        </div>
                        <i class="fas fa-map-marker-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-left-success shadow h-100 py-2" style="border-left: 4px solid #1cc88a;">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0 text-success" id="activeLocations">{{ locations|selectattr('status', 'equalto', 'نشط')|list|length }}</h4>
                            <p class="mb-0 text-muted">المواقع النشطة</p>
                        </div>
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-left-warning shadow h-100 py-2" style="border-left: 4px solid #f6c23e;">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0 text-warning" id="totalPersonnel">
                                {% set total_personnel = 0 %}
                                {% for location in locations %}
                                    {% set total_personnel = total_personnel + location.personnel_assignments.filter_by(is_active=True).count() %}
                                {% endfor %}
                                {{ total_personnel }}
                            </h4>
                            <p class="mb-0 text-muted">إجمالي الأفراد</p>
                        </div>
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-left-info shadow h-100 py-2" style="border-left: 4px solid #36b9cc;">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0 text-info" id="totalEquipment">
                                {% set total_equipment = 0 %}
                                {% for location in locations %}
                                    {% set total_equipment = total_equipment + location.equipment.count() %}
                                {% endfor %}
                                {{ total_equipment }}
                            </h4>
                            <p class="mb-0 text-muted">إجمالي العهد</p>
                        </div>
                        <i class="fas fa-boxes fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Locations List -->
    <div id="locationsContainer" class="locations-grid">
        {% if locations %}
            {% for location in locations %}
            <div class="location-card" data-name="{{ location.name|lower }}" data-serial="{{ location.serial_number|lower }}" data-type="{{ location.type }}" data-status="{{ location.status }}" data-equipment-count="{{ location.equipment.count() }}" data-personnel-count="{{ location.personnel_assignments.filter_by(is_active=True).count() }}">
                <div class="location-header">
                    <div>
                        <h3 class="location-title">{{ location.name }}</h3>
                        <p class="location-serial">الرقم التسلسلي: {{ location.serial_number }}</p>
                    </div>
                    <span class="status-badge status-{{ 'active' if location.status == 'نشط' else 'inactive' if location.status == 'غير نشط' else 'withdrawn' if location.status == 'مسحوب' else 'maintenance' }}">
                        {{ location.status }}
                    </span>
                </div>

                <div class="location-info">
                    <div class="info-item">
                        <i class="fas fa-tag info-icon"></i>
                        <span>{{ location.type }}</span>
                    </div>
                    {% if location.coordinates %}
                    <div class="info-item">
                        <i class="fas fa-map-pin info-icon"></i>
                        <span>{{ location.coordinates }}</span>
                    </div>
                    {% endif %}
                    <div class="info-item">
                        <i class="fas fa-calendar info-icon"></i>
                        <span>{{ location.created_at.strftime('%Y-%m-%d') if location.created_at else 'غير محدد' }}</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-boxes info-icon"></i>
                        <span>{{ location.equipment.count() }} عهدة</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-users info-icon"></i>
                        <span>{{ location.personnel_assignments.filter_by(is_active=True).count() }} فرد</span>
                    </div>
                </div>

                {% if location.description %}
                <div class="mt-2">
                    <small class="text-muted">{{ location.description }}</small>
                </div>
                {% endif %}

                {% if location.equipment.count() > 0 %}
                <div class="equipment-section">
                    <small class="text-muted">العهد المخصصة:</small>
                    <div class="equipment-list">
                        {% for equipment in location.equipment.limit(5) %}
                        <span class="equipment-tag">{{ equipment.equipment_name }}</span>
                        {% endfor %}
                        {% if location.equipment.count() > 5 %}
                        <span class="equipment-tag">+{{ location.equipment.count() - 5 }} أخرى</span>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <div class="action-buttons">
                    <a href="{{ url_for('locations.view_location', location_id=location.id) }}" class="btn-action btn-view">
                        <i class="fas fa-eye"></i> عرض
                    </a>
                    <a href="{{ url_for('locations.edit_location', location_id=location.id) }}" class="btn-action btn-edit">
                        <i class="fas fa-edit"></i> تعديل
                    </a>
                    <button type="button" class="btn-action btn-delete" onclick="deleteLocation({{ location.id }}, '{{ location.name }}')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="empty-state">
                <i class="fas fa-map-marker-alt"></i>
                <h3>لا توجد مواقع</h3>
                <p>لم يتم إضافة أي مواقع بعد. ابدأ بإضافة موقع جديد.</p>
                <a href="{{ url_for('locations.add_location') }}" class="add-location-btn mt-3">
                    <i class="fas fa-plus"></i>
                    إضافة أول موقع
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الموقع "<span id="locationNameToDelete"></span>"؟</p>
                <p class="text-danger"><small>سيتم حذف جميع العهد المرتبطة بهذا الموقع أيضاً.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">حذف</button>
            </div>
        </div>
    </div>
</div>

<script>
// Search and Filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const typeFilter = document.getElementById('typeFilter');
    const statusFilter = document.getElementById('statusFilter');
    const clearFiltersBtn = document.getElementById('clearFilters');
    const locationCards = document.querySelectorAll('.location-card');

    function filterLocations() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedType = typeFilter.value;
        const selectedStatus = statusFilter.value;

        locationCards.forEach(card => {
            const name = card.dataset.name;
            const serial = card.dataset.serial;
            const type = card.dataset.type;
            const status = card.dataset.status;

            const matchesSearch = name.includes(searchTerm) || serial.includes(searchTerm);
            const matchesType = !selectedType || type === selectedType;
            const matchesStatus = !selectedStatus || status === selectedStatus;

            if (matchesSearch && matchesType && matchesStatus) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });

        updateStats();
    }

    function updateStats() {
        // البحث عن جميع البطاقات المرئية (التي لا تحتوي على display: none)
        const allCards = document.querySelectorAll('.location-card');
        const visibleCards = Array.from(allCards).filter(card => {
            return card.style.display !== 'none';
        });

        const totalVisible = visibleCards.length;

        let activeCount = 0;
        let maintenanceCount = 0;
        let totalEquipment = 0;
        let totalPersonnel = 0;

        visibleCards.forEach(card => {
            const status = card.dataset.status;
            if (status === 'نشط') activeCount++;
            if (status === 'تحت الصيانة') maintenanceCount++;

            // حساب العهد الفعلي من data attribute
            const equipmentCount = parseInt(card.dataset.equipmentCount) || 0;
            totalEquipment += equipmentCount;

            // حساب الأفراد الفعلي من data attribute
            const personnelCount = parseInt(card.dataset.personnelCount) || 0;
            totalPersonnel += personnelCount;
        });

        document.getElementById('totalLocations').textContent = totalVisible;
        document.getElementById('activeLocations').textContent = activeCount;
        document.getElementById('totalPersonnel').textContent = totalPersonnel;
        document.getElementById('totalEquipment').textContent = totalEquipment;
    }

    function clearFilters() {
        searchInput.value = '';
        typeFilter.value = '';
        statusFilter.value = '';
        filterLocations();
    }

    // Event listeners
    searchInput.addEventListener('input', filterLocations);
    typeFilter.addEventListener('change', filterLocations);
    statusFilter.addEventListener('change', filterLocations);
    clearFiltersBtn.addEventListener('click', clearFilters);

    // تحديث الإحصائيات عند تحميل الصفحة
    updateStats();
});

// Delete location function
function deleteLocation(id, name) {
    document.getElementById('locationNameToDelete').textContent = name;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();

    document.getElementById('confirmDelete').onclick = function() {
        fetch(`/locations/${id}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('تم حذف الموقع بنجاح', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showNotification(data.message || 'حدث خطأ أثناء حذف الموقع', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('حدث خطأ أثناء حذف الموقع', 'error');
        });

        modal.hide();
    };
}
</script>
{% endblock %}
