{% extends "base.html" %}

{% block content %}
<div class="display-screen">
    <div class="display-header">
        <div class="header-content">
            <div class="logo-section">
                <img src="{{ url_for('static', filename='images/KSA.png') }}" alt="شعار" class="logo">
                <h1>{{ warehouse.name }}</h1>
            </div>
            <div class="time-section">
                <div class="current-time" id="current-time"></div>
                <div class="current-date" id="current-date"></div>
            </div>
        </div>
        <div class="status-banner">
            <div class="status-item">
                <i class="fas fa-crosshairs"></i>
                <span>الأسلحة النشطة: <strong>{{ weapon_status.active }}</strong></span>
            </div>
            <div class="status-item">
                <i class="fas fa-users"></i>
                <span>الأفراد الحاضرون: <strong>{{ active_personnel }}</strong></span>
            </div>
            <div class="status-item">
                <i class="fas fa-sync"></i>
                <span>آخر تحديث: <strong><span id="last-update"></span></strong></span>
            </div>
        </div>
    </div>

    <div class="display-content">
        <div class="row">
            <div class="col-md-6">
                <div class="stat-panel weapons-panel">
                    <div class="panel-header">
                        <h2><i class="fas fa-crosshairs"></i> الأسلحة</h2>
                        <div class="count">{{ weapons_count }}</div>
                    </div>
                    <div class="panel-body">
                        <div class="chart-container">
                            <canvas id="weaponsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="stat-panel personnel-panel">
                    <div class="panel-header">
                        <h2><i class="fas fa-users"></i> الأفراد</h2>
                        <div class="count">{{ personnel_count }}</div>
                    </div>
                    <div class="panel-body">
                        <div class="status-bars">
                            <div class="status-bar">
                                <div class="label">نشط</div>
                                <div class="progress">
                                    <div class="progress-bar bg-success" style="width: {{ (active_personnel / personnel_count * 100) if personnel_count else 0 }}%"></div>
                                </div>
                                <div class="count">{{ active_personnel }}</div>
                            </div>
                            <div class="status-bar">
                                <div class="label">إجازة</div>
                                <div class="progress">
                                    <div class="progress-bar bg-warning" style="width: {{ (leave_personnel / personnel_count * 100) if personnel_count else 0 }}%"></div>
                                </div>
                                <div class="count">{{ leave_personnel }}</div>
                            </div>
                            <div class="status-bar">
                                <div class="label">مهمة</div>
                                <div class="progress">
                                    <div class="progress-bar bg-info" style="width: {{ (mission_personnel / personnel_count * 100) if personnel_count else 0 }}%"></div>
                                </div>
                                <div class="count">{{ mission_personnel }}</div>
                            </div>
                            <div class="status-bar">
                                <div class="label">دورة</div>
                                <div class="progress">
                                    <div class="progress-bar bg-danger" style="width: {{ (cycle_personnel / personnel_count * 100) if personnel_count else 0 }}%"></div>
                                </div>
                                <div class="count">{{ cycle_personnel|default(0) }}</div>
                            </div>
                            <div class="status-bar">
                                <div class="label">مستلم</div>
                                <div class="progress">
                                    <div class="progress-bar bg-primary" style="width: {{ (stoped_personnel / personnel_count * 100) if personnel_count else 0 }}%"></div>
                                </div>
                                <div class="count">{{ stoped_personnel }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="stat-panel transactions-panel">
                    <div class="panel-header">
                        <h2><i class="fas fa-exchange-alt"></i> آخر المعاملات</h2>
                        <button class="panel-toggle-btn" data-target="transactions-body" title="طي">
                            <i class="fas fa-chevron-up"></i>
                        </button>
                    </div>
                    <div class="panel-body" id="transactions-body">
                        <div class="transactions-list">
                            {% for transaction in recent_transactions %}
                            <div class="transaction-item">

                                <div class="transaction-icon">
                                    {% if transaction.transaction_type == 'checkout' %}
                                    <i class="fas fa-arrow-right"></i>
                                    {% elif transaction.transaction_type == 'return' %}
                                    <i class="fas fa-arrow-left"></i>
                                    {% elif transaction.transaction_type == 'transfer' %}
                                    <i class="fas fa-exchange-alt"></i>
                                    {% endif %}
                                </div>
                                <div class="transaction-content">
                                    <div class="transaction-title">
                                        {% if transaction.transaction_type == 'checkout' %}
                                        تم تسليم
                                        {% elif transaction.transaction_type == 'return' %}
                                        تم استلام
                                        {% elif transaction.transaction_type == 'transfer' %}
                                        تم نقل
                                        {% endif %}
                                        {{ transaction.weapon.name }} ({{ transaction.weapon.serial_number }})
                                    </div>
                                    <div class="transaction-details">
                                        {{ transaction.personnel.name }} ({{ transaction.personnel.personnel_id }})
                                    </div>
                                </div>
                            </div>
                            {% else %}
                            <div class="no-data">لا توجد معاملات حتى الآن</div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="display-footer">
        <div class="footer-info">
            <div>{{ warehouse.name }}</div>
            <div>نظام عتاد</div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set up the current time
        function updateTime() {
            const now = new Date();
            const timeElement = document.getElementById('current-time');
            const dateElement = document.getElementById('current-date');

            // Format time: HH:MM:SS
            const timeOptions = {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            };
            timeElement.textContent = now.toLocaleTimeString('ar-SA', timeOptions);

            // Format date: Day, DD Month YYYY
            const dateOptions = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            dateElement.textContent = now.toLocaleDateString('ar-SA', dateOptions);

            // Update last update time
            document.getElementById('last-update').textContent = now.toLocaleTimeString('ar-SA');
        }

        updateTime();
        setInterval(updateTime, 1000);

        // Set up weapons chart
        const weaponsCtx = document.getElementById('weaponsChart').getContext('2d');
        new Chart(weaponsCtx, {
            type: 'doughnut',
            data: {
                labels: ['نشط', 'إجازة', 'مهمة', 'صيانة', 'دورة', 'شاغر', 'مستلم', 'رماية'],
                datasets: [{
                    data: [{{ weapon_status.active }}, {{ weapon_status.leave }}, {{ weapon_status.mission }}, {{ weapon_status.maintenance }}, {{ weapon_status.cycle }}, {{ weapon_status.vacant }}, {{ weapon_status.recipient|default(0) }}, {{ weapon_status.shooting|default(0) }}],
                    backgroundColor: [
                        '#28a745', // نشط - أخضر
                        '#ffc107', // إجازة - أصفر (تم توحيد اللون)
                        '#fd7e14', // مهمة - برتقالي
                        '#FAAFBE', // صيانة - وردي
                        '#dc3545', // دورة - أحمر
                        '#343a40',  // شاغر - أسود
                        '#0d6efd',   // مستلم - أزرق
                        '#C8BBBE'   // رماية - Lavender Blush3
                    ],
                    borderColor: [
                        '#ffffff',
                        '#ffffff',
                        '#ffffff',
                        '#ffffff',
                        '#ffffff',
                        '#ffffff',
                        '#ffffff',
                        '#ffffff'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    position: 'bottom',
                    labels: {
                        fontFamily: 'Cairo, sans-serif',
                        fontSize: 14,
                        fontColor: '#ffffff'
                    }
                }
            }
        });

        // Periodically refresh the data
        function refreshData() {
            fetch('/api/warehouse/{{ warehouse.id }}/stats')
                .then(response => response.json())
                .then(data => {
                    // Update counts and statistics
                    console.log("Refreshed data:", data);
                    // Implement real-time updates here
                })
                .catch(error => {
                    console.error('Error fetching updated data:', error);
                });
        }

        // Refresh every 30 seconds
        setInterval(refreshData, 30000);
    });
</script>
{% endblock %}
