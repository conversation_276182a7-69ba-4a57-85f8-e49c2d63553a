// كشف الاستلامات المنفصل - JavaScript Functions
// 🚀 بدء تحميل ملف receipts-isolated.js (منفصل عن كشف الواجبات)
console.log('🚀 تحميل ملف receipts-isolated.js...');

// العناوين الافتراضية لكشف الاستلامات (منفصلة تماماً)
const RECEIPTS_DEFAULT_HEADERS = ['الرقم', 'موقع الاستلام', 'من 6 مساءً إلى 10 ليلاً', 'من 10 ليلاً إلى 2 ليلاً', 'من 2 ليلاً إلى 6 صباحاً', 'من 6 صباحاً إلى 10 صباحاً', 'من 10 صباحاً إلى 2 ظهراً', 'من 2 ظهراً إلى 6 مساءً', 'ملاحظات'];

// العناوين الافتراضية لجدول دوريات الاستلامات
const RECEIPTS_PATROL_HEADERS = ['الرقم', 'موقع الاستلام', '6 مساءً إلى 12 ليلاً', '12 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 12 ظهراً', '12 ظهراً إلى 6 مساءً', 'ملاحظات'];

// العناوين الافتراضية لجدول مناوبين الاستلامات
const RECEIPTS_SHIFTS_HEADERS = ['الرقم', 'موقع الاستلام', '2 ظهراً إلى 10 ليلاً', '10 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 2 ظهراً', 'ملاحظات المناوبين'];

console.log('✅ تم تحديد عناوين كشف الاستلامات المنفصلة');
console.log('📋 عناوين كشف الاستلامات:', RECEIPTS_DEFAULT_HEADERS);
console.log('📋 عناوين دوريات الاستلامات:', RECEIPTS_PATROL_HEADERS);
console.log('📋 عناوين مناوبين الاستلامات:', RECEIPTS_SHIFTS_HEADERS);

// بيانات كشف الاستلامات (منفصلة تماماً)
let receiptsMainData = {
    hijriDate: '',
    gregorianDate: '',
    receiptNumber: '',
    headers: [
        [...RECEIPTS_DEFAULT_HEADERS]
    ],
    rows: [],
    patrolNotes: [],
    guardNotes: []
};

// بيانات جدول دوريات الاستلامات (منفصلة تماماً)
let receiptsPatrolData = {
    headers: [...RECEIPTS_PATROL_HEADERS],
    rows: []
};

// بيانات جدول مناوبين الاستلامات (منفصلة تماماً)
let receiptsShiftsData = {
    headers: [...RECEIPTS_SHIFTS_HEADERS],
    rows: []
};

console.log('📊 تم تهيئة بيانات كشف الاستلامات المنفصلة:', receiptsMainData);
console.log('📊 تم تهيئة بيانات دوريات الاستلامات:', receiptsPatrolData);
console.log('📊 تم تهيئة بيانات مناوبين الاستلامات:', receiptsShiftsData);

// قاعدة بيانات المواقع والأفراد (منفصلة)
let receiptsLocationsDatabase = [];
let receiptsLocationPersonnelMap = {};

// متغيرات الحفظ (منفصلة)
let receiptsSaveTimeouts = {
    main: null,
    patrol: null,
    shifts: null
};

// دالة لإعادة تعيين عناوين كشف الاستلامات
function resetReceiptsHeaders() {
    console.log('🔄 إعادة تعيين عناوين كشف الاستلامات...');
    
    receiptsMainData.headers = [
        [...RECEIPTS_DEFAULT_HEADERS]
    ];
    receiptsPatrolData.headers = [...RECEIPTS_PATROL_HEADERS];
    receiptsShiftsData.headers = [...RECEIPTS_SHIFTS_HEADERS];
    
    console.log('✅ تم إعادة تعيين عناوين كشف الاستلامات بنجاح');
}

// دالة حفظ بيانات دوريات الاستلامات
function saveReceiptsPatrolData() {
    if (receiptsSaveTimeouts.patrol) {
        clearTimeout(receiptsSaveTimeouts.patrol);
    }
    
    receiptsSaveTimeouts.patrol = setTimeout(() => {
        console.log('💾 حفظ بيانات دوريات الاستلامات...');
        
        try {
            fetch('/receipts/api/save-patrol-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(receiptsPatrolData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('✅ تم حفظ بيانات دوريات الاستلامات:', data);
            })
            .catch(error => {
                console.error('❌ خطأ في حفظ دوريات الاستلامات:', error);
            });
        } catch (error) {
            console.error('❌ خطأ في إرسال دوريات الاستلامات:', error);
        }
        
        receiptsSaveTimeouts.patrol = null;
    }, 300);
}

// دالة حفظ بيانات مناوبين الاستلامات
function saveReceiptsShiftsData() {
    if (receiptsSaveTimeouts.shifts) {
        clearTimeout(receiptsSaveTimeouts.shifts);
    }
    
    receiptsSaveTimeouts.shifts = setTimeout(() => {
        console.log('💾 حفظ بيانات مناوبين الاستلامات...');
        
        try {
            fetch('/receipts/api/save-shifts-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(receiptsShiftsData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('✅ تم حفظ بيانات مناوبين الاستلامات:', data);
            })
            .catch(error => {
                console.error('❌ خطأ في حفظ مناوبين الاستلامات:', error);
            });
        } catch (error) {
            console.error('❌ خطأ في إرسال مناوبين الاستلامات:', error);
        }
        
        receiptsSaveTimeouts.shifts = null;
    }, 300);
}

// دالة حفظ جميع بيانات الاستلامات
function saveAllReceiptsData() {
    console.log('💾 حفظ جميع بيانات كشف الاستلامات...');
    
    // حفظ في localStorage (منفصل)
    localStorage.setItem('receiptsMainData', JSON.stringify(receiptsMainData));
    localStorage.setItem('receiptsPatrolData', JSON.stringify(receiptsPatrolData));
    localStorage.setItem('receiptsShiftsData', JSON.stringify(receiptsShiftsData));
    localStorage.setItem('receiptsLastSaved', new Date().toISOString());
    
    // حفظ في الخادم
    const allData = {
        ...receiptsMainData,
        patrolTableData: receiptsPatrolData,
        shiftsTableData: receiptsShiftsData
    };
    
    fetch('/receipts/api/save-receipt-data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(allData)
    })
    .then(response => response.json())
    .then(data => {
        console.log('✅ تم حفظ جميع بيانات كشف الاستلامات:', data);
    })
    .catch(error => {
        console.error('❌ خطأ في حفظ بيانات كشف الاستلامات:', error);
    });
    
    // حفظ البيانات الفرعية
    saveReceiptsPatrolData();
    saveReceiptsShiftsData();
}

// دالة تحديث خلية في جدول دوريات الاستلامات
function updateReceiptsPatrolCell(rowIndex, columnIndex, newValue) {
    if (!receiptsPatrolData.rows[rowIndex]) {
        receiptsPatrolData.rows[rowIndex] = Array(receiptsPatrolData.headers.length).fill('');
    }
    
    receiptsPatrolData.rows[rowIndex][columnIndex] = newValue;
    console.log(`📝 تم تحديث خلية دوريات الاستلامات [${rowIndex}, ${columnIndex}]: "${newValue}"`);
    
    // حفظ فوري
    localStorage.setItem('receiptsPatrolData', JSON.stringify(receiptsPatrolData));
    saveReceiptsPatrolData();
}

// دالة تحديث خلية في جدول مناوبين الاستلامات
function updateReceiptsShiftsCell(rowIndex, columnIndex, newValue) {
    if (!receiptsShiftsData.rows[rowIndex]) {
        receiptsShiftsData.rows[rowIndex] = Array(receiptsShiftsData.headers.length).fill('');
    }
    
    receiptsShiftsData.rows[rowIndex][columnIndex] = newValue;
    console.log(`📝 تم تحديث خلية مناوبين الاستلامات [${rowIndex}, ${columnIndex}]: "${newValue}"`);
    
    // حفظ فوري
    localStorage.setItem('receiptsShiftsData', JSON.stringify(receiptsShiftsData));
    saveReceiptsShiftsData();
}

// دالة تهيئة بيانات كشف الاستلامات
function initializeReceiptsData() {
    console.log('🔧 تهيئة بيانات كشف الاستلامات...');
    
    // تهيئة الصفوف الافتراضية للجدول الرئيسي
    if (receiptsMainData.rows.length === 0) {
        for (let i = 1; i <= 18; i++) {
            receiptsMainData.rows.push(Array(receiptsMainData.headers[0].length).fill(''));
        }
    }
    
    // تهيئة الصفوف الافتراضية لجدول الدوريات
    if (receiptsPatrolData.rows.length === 0) {
        for (let i = 0; i < 6; i++) {
            receiptsPatrolData.rows.push(Array(receiptsPatrolData.headers.length).fill(''));
        }
    }
    
    // تهيئة الصفوف الافتراضية لجدول المناوبين
    if (receiptsShiftsData.rows.length === 0) {
        receiptsShiftsData.rows.push(Array(receiptsShiftsData.headers.length).fill(''));
    }
    
    console.log('✅ تم تهيئة بيانات كشف الاستلامات');
}

// دالة تحميل بيانات كشف الاستلامات من localStorage
function loadReceiptsDataFromStorage() {
    console.log('📥 تحميل بيانات كشف الاستلامات من التخزين المحلي...');
    
    try {
        const savedMainData = localStorage.getItem('receiptsMainData');
        const savedPatrolData = localStorage.getItem('receiptsPatrolData');
        const savedShiftsData = localStorage.getItem('receiptsShiftsData');
        
        if (savedMainData) {
            receiptsMainData = { ...receiptsMainData, ...JSON.parse(savedMainData) };
            console.log('✅ تم تحميل البيانات الرئيسية لكشف الاستلامات');
        }
        
        if (savedPatrolData) {
            receiptsPatrolData = { ...receiptsPatrolData, ...JSON.parse(savedPatrolData) };
            console.log('✅ تم تحميل بيانات دوريات الاستلامات');
        }
        
        if (savedShiftsData) {
            receiptsShiftsData = { ...receiptsShiftsData, ...JSON.parse(savedShiftsData) };
            console.log('✅ تم تحميل بيانات مناوبين الاستلامات');
        }
        
        return true;
    } catch (error) {
        console.error('❌ خطأ في تحميل بيانات كشف الاستلامات:', error);
        return false;
    }
}

// دالة إنشاء جدول دوريات الاستلامات
function generateReceiptsPatrolTable() {
    console.log('🔨 إنشاء جدول دوريات الاستلامات...');

    const thead = document.getElementById('patrolTableHeader');
    const tbody = document.getElementById('patrolTableBody');

    if (!thead || !tbody) {
        console.error('❌ عناصر جدول الدوريات غير موجودة');
        return;
    }

    // إنشاء العناوين
    thead.innerHTML = '';
    const headerRow = document.createElement('tr');

    receiptsPatrolData.headers.forEach((headerText, index) => {
        const th = document.createElement('th');
        th.className = 'text-center align-middle';
        th.textContent = headerText;
        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);

    // إنشاء الصفوف
    tbody.innerHTML = '';

    receiptsPatrolData.rows.forEach((rowData, rowIndex) => {
        const row = document.createElement('tr');

        rowData.forEach((cellData, cellIndex) => {
            const td = document.createElement('td');

            if (cellIndex === 0) {
                // عمود الرقم
                td.innerHTML = `<span class="row-number">${rowIndex + 1}</span>`;
                td.className = 'text-center align-middle';
            } else if (cellIndex === 1) {
                // عمود الموقع - قائمة منسدلة
                td.innerHTML = `
                    <select class="form-select receipts-patrol-location-select"
                            onchange="updateReceiptsPatrolCell(${rowIndex}, ${cellIndex}, this.value)">
                        <option value="">اختر الموقع</option>
                    </select>
                `;
                td.className = 'text-center align-middle';
            } else {
                // الخلايا النصية
                td.innerHTML = `
                    <input type="text" class="form-control receipts-patrol-editable-cell"
                           value="${cellData || ''}"
                           onchange="updateReceiptsPatrolCell(${rowIndex}, ${cellIndex}, this.value)"
                           placeholder="أدخل النص هنا">
                `;
                td.className = 'text-center align-middle';
            }

            row.appendChild(td);
        });

        tbody.appendChild(row);
    });

    console.log('✅ تم إنشاء جدول دوريات الاستلامات');
}

// دالة إنشاء جدول مناوبين الاستلامات
function generateReceiptsShiftsTable() {
    console.log('🔨 إنشاء جدول مناوبين الاستلامات...');

    const thead = document.getElementById('shiftsTableHeader');
    const tbody = document.getElementById('shiftsTableBody');

    if (!thead || !tbody) {
        console.error('❌ عناصر جدول المناوبين غير موجودة');
        return;
    }

    // إنشاء العناوين
    thead.innerHTML = '';
    const headerRow = document.createElement('tr');

    receiptsShiftsData.headers.forEach((headerText, index) => {
        const th = document.createElement('th');
        th.className = 'text-center align-middle';
        th.textContent = headerText;
        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);

    // إنشاء الصفوف
    tbody.innerHTML = '';

    receiptsShiftsData.rows.forEach((rowData, rowIndex) => {
        const row = document.createElement('tr');

        rowData.forEach((cellData, cellIndex) => {
            const td = document.createElement('td');

            if (cellIndex === 0) {
                // عمود الرقم
                td.innerHTML = `<span class="row-number">${rowIndex + 1}</span>`;
                td.className = 'text-center align-middle';
            } else if (cellIndex === 1) {
                // عمود الموقع - قائمة منسدلة
                td.innerHTML = `
                    <select class="form-select receipts-shifts-location-select"
                            onchange="updateReceiptsShiftsCell(${rowIndex}, ${cellIndex}, this.value)">
                        <option value="">اختر الموقع</option>
                    </select>
                `;
                td.className = 'text-center align-middle';
            } else {
                // الخلايا النصية
                td.innerHTML = `
                    <input type="text" class="form-control receipts-shifts-editable-cell"
                           value="${cellData || ''}"
                           onchange="updateReceiptsShiftsCell(${rowIndex}, ${cellIndex}, this.value)"
                           placeholder="أدخل النص هنا">
                `;
                td.className = 'text-center align-middle';
            }

            row.appendChild(td);
        });

        tbody.appendChild(row);
    });

    console.log('✅ تم إنشاء جدول مناوبين الاستلامات');
}

// دالة تحميل المواقع من الخادم
function loadReceiptsLocations() {
    console.log('📥 تحميل مواقع كشف الاستلامات...');

    fetch('/locations/api/get-locations')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                receiptsLocationsDatabase = data.locations;
                console.log('✅ تم تحميل مواقع كشف الاستلامات:', receiptsLocationsDatabase.length);

                // تحديث القوائم المنسدلة
                updateReceiptsLocationSelects();
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل مواقع كشف الاستلامات:', error);
        });
}

// دالة تحديث القوائم المنسدلة للمواقع
function updateReceiptsLocationSelects() {
    // تحديث قوائم دوريات الاستلامات
    const patrolSelects = document.querySelectorAll('.receipts-patrol-location-select');
    patrolSelects.forEach(select => {
        const currentValue = select.value;
        select.innerHTML = '<option value="">اختر الموقع</option>';

        receiptsLocationsDatabase.forEach(location => {
            const option = document.createElement('option');
            option.value = location.id;
            option.textContent = location.name;
            if (location.id == currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    });

    // تحديث قوائم مناوبين الاستلامات
    const shiftsSelects = document.querySelectorAll('.receipts-shifts-location-select');
    shiftsSelects.forEach(select => {
        const currentValue = select.value;
        select.innerHTML = '<option value="">اختر الموقع</option>';

        receiptsLocationsDatabase.forEach(location => {
            const option = document.createElement('option');
            option.value = location.id;
            option.textContent = location.name;
            if (location.id == currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    });
}

// تهيئة كشف الاستلامات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء تهيئة كشف الاستلامات المنفصل...');

    // تهيئة البيانات
    initializeReceiptsData();

    // تحميل البيانات المحفوظة
    loadReceiptsDataFromStorage();

    // تحميل المواقع
    loadReceiptsLocations();

    // إنشاء الجداول
    setTimeout(() => {
        generateReceiptsPatrolTable();
        generateReceiptsShiftsTable();
    }, 500);

    // حفظ دوري
    setInterval(() => {
        saveAllReceiptsData();
    }, 60000);

    console.log('✅ تم تهيئة كشف الاستلامات المنفصل بنجاح');
});

console.log('✅ تم تحميل ملف receipts-isolated.js بنجاح');
