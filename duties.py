from flask import Blueprint, render_template, request, jsonify, session, send_file, make_response
from flask_login import login_required, current_user
from datetime import datetime, date, time
import json
from io import BytesIO
import csv
import tempfile
import os
from db import db
from models import DutyData, DutyPersonnel, DutyTemplate, Location, Personnel, LocationPersonnel
from datetime_utils import get_saudi_now

# محاولة استيراد مكتبات Excel
try:
    import pandas as pd
    import xlsxwriter
    EXCEL_AVAILABLE = True
    print("✅ مكتبات Excel متوفرة")
except ImportError:
    EXCEL_AVAILABLE = False
    print("⚠️ مكتبات Excel غير متوفرة، سيتم استخدام CSV كبديل")

# إنشاء Blueprint
duties_bp = Blueprint('duties', __name__, url_prefix='/duties')

@duties_bp.route('/')
@login_required
def index():
    """صفحة كشف الواجبات الرئيسية"""
    return render_template('duties.html')

@duties_bp.route('/api/get-locations', methods=['GET'])
@login_required
def get_locations():
    """الحصول على قائمة المواقع المتاحة"""
    try:
        locations = Location.query.filter_by(status='نشط').order_by(Location.name).all()
        
        locations_data = []
        for location in locations:
            locations_data.append({
                'id': location.id,
                'name': location.name,
                'serial_number': location.serial_number,
                'type': location.type,
                'description': location.description
            })
        
        return jsonify({
            'success': True,
            'locations': locations_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/get-location-personnel/<int:location_id>', methods=['GET'])
@login_required
def get_location_personnel(location_id):
    """الحصول على الأفراد المعينين لموقع معين"""
    try:
        print(f"🔍 البحث عن أفراد الموقع {location_id}")

        # الحصول على الأفراد المرتبطين بالموقع من خلال جدول LocationPersonnel
        # جلب جميع الأفراد المعينين للموقع بغض النظر عن حالتهم
        personnel_assignments = db.session.query(LocationPersonnel, Personnel).join(
            Personnel, LocationPersonnel.personnel_id == Personnel.id
        ).filter(
            LocationPersonnel.location_id == location_id,
            LocationPersonnel.is_active == True
        ).all()

        print(f"📊 تم العثور على {len(personnel_assignments)} تعيين نشط")

        personnel_data = []
        for assignment, person in personnel_assignments:
            # إضافة رمز للحالة
            status_icon = '✅' if person.status == 'نشط' else '⚠️'
            display_name = f"{person.rank} {person.name}"
            if person.status != 'نشط':
                display_name += f" ({person.status})"

            personnel_data.append({
                'id': person.id,
                'personnel_id': person.personnel_id,
                'name': person.name,
                'rank': person.rank,
                'phone': person.phone,
                'status': person.status,
                'display_name': display_name,
                'assignment_date': assignment.assignment_date.strftime('%Y-%m-%d') if assignment.assignment_date else None
            })
            print(f"👤 {status_icon} {person.rank} {person.name} - حالة: {person.status} - معين منذ {assignment.assignment_date.strftime('%Y-%m-%d') if assignment.assignment_date else 'غير محدد'}")

        print(f"✅ إرسال {len(personnel_data)} فرد للعميل")

        return jsonify({
            'success': True,
            'personnel': personnel_data
        })

    except Exception as e:
        print(f"❌ خطأ في جلب أفراد الموقع: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/save-duty', methods=['POST'])
@login_required
def save_duty():
    """حفظ كشف واجب جديد"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'error': 'لا توجد بيانات'})
        
        # التحقق من البيانات المطلوبة
        required_fields = ['location_id', 'duty_date', 'duty_time', 'duty_data']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'حقل مطلوب مفقود: {field}'})
        
        # تحويل التاريخ والوقت
        try:
            duty_date = datetime.strptime(data['duty_date'], '%Y-%m-%d').date()
            duty_time = datetime.strptime(data['duty_time'], '%H:%M').time()
        except ValueError as e:
            return jsonify({'success': False, 'error': f'تنسيق التاريخ أو الوقت غير صحيح: {str(e)}'})
        
        # إنشاء سجل كشف الواجب
        duty_data = DutyData(
            location_id=data['location_id'],
            user_id=current_user.id,
            duty_date=duty_date,
            duty_time=duty_time,
            duty_data=json.dumps(data['duty_data'], ensure_ascii=False),
            notes=data.get('notes', '')
        )
        
        db.session.add(duty_data)
        db.session.flush()  # للحصول على ID
        
        # حفظ أفراد الواجب
        if 'personnel' in data and data['personnel']:
            for person_data in data['personnel']:
                duty_personnel = DutyPersonnel(
                    duty_data_id=duty_data.id,
                    personnel_id=person_data['personnel_id'],
                    duty_position=person_data.get('position', ''),
                    duty_status=person_data.get('status', 'حاضر'),
                    notes=person_data.get('notes', '')
                )
                db.session.add(duty_personnel)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم حفظ كشف الواجب بنجاح',
            'duty_id': duty_data.id
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/get-duties', methods=['GET'])
@login_required
def get_duties():
    """الحصول على قائمة كشوفات الواجبات"""
    try:
        # معاملات البحث
        location_id = request.args.get('location_id', type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # بناء الاستعلام
        query = db.session.query(DutyData, Location).join(
            Location, DutyData.location_id == Location.id
        )
        
        # تطبيق المرشحات
        if location_id:
            query = query.filter(DutyData.location_id == location_id)
        
        if start_date:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(DutyData.duty_date >= start_date_obj)
        
        if end_date:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(DutyData.duty_date <= end_date_obj)
        
        # ترتيب النتائج
        query = query.order_by(DutyData.duty_date.desc(), DutyData.duty_time.desc())
        
        duties = query.all()
        
        duties_data = []
        for duty_data, location in duties:
            # الحصول على أفراد الواجب
            personnel = db.session.query(DutyPersonnel, Personnel).join(
                Personnel, DutyPersonnel.personnel_id == Personnel.id
            ).filter(DutyPersonnel.duty_data_id == duty_data.id).all()
            
            personnel_list = []
            for duty_personnel, person in personnel:
                personnel_list.append({
                    'id': person.id,
                    'name': person.name,
                    'rank': person.rank,
                    'position': duty_personnel.duty_position,
                    'status': duty_personnel.duty_status,
                    'notes': duty_personnel.notes
                })
            
            duties_data.append({
                'id': duty_data.id,
                'location': {
                    'id': location.id,
                    'name': location.name,
                    'serial_number': location.serial_number
                },
                'duty_date': duty_data.duty_date.strftime('%Y-%m-%d'),
                'duty_time': duty_data.duty_time.strftime('%H:%M'),
                'duty_data': json.loads(duty_data.duty_data),
                'personnel': personnel_list,
                'notes': duty_data.notes,
                'created_at': duty_data.created_at.strftime('%Y-%m-%d %H:%M')
            })
        
        return jsonify({
            'success': True,
            'duties': duties_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/get-duty/<int:duty_id>', methods=['GET'])
@login_required
def get_duty(duty_id):
    """الحصول على تفاصيل كشف واجب محدد"""
    try:
        duty_data = DutyData.query.get_or_404(duty_id)
        location = Location.query.get(duty_data.location_id)
        
        # الحصول على أفراد الواجب
        personnel = db.session.query(DutyPersonnel, Personnel).join(
            Personnel, DutyPersonnel.personnel_id == Personnel.id
        ).filter(DutyPersonnel.duty_data_id == duty_id).all()
        
        personnel_list = []
        for duty_personnel, person in personnel:
            personnel_list.append({
                'id': person.id,
                'personnel_id': person.personnel_id,
                'name': person.name,
                'rank': person.rank,
                'position': duty_personnel.duty_position,
                'status': duty_personnel.duty_status,
                'notes': duty_personnel.notes
            })
        
        duty_info = {
            'id': duty_data.id,
            'location': {
                'id': location.id,
                'name': location.name,
                'serial_number': location.serial_number
            },
            'duty_date': duty_data.duty_date.strftime('%Y-%m-%d'),
            'duty_time': duty_data.duty_time.strftime('%H:%M'),
            'duty_data': json.loads(duty_data.duty_data),
            'personnel': personnel_list,
            'notes': duty_data.notes,
            'created_at': duty_data.created_at.strftime('%Y-%m-%d %H:%M'),
            'updated_at': duty_data.updated_at.strftime('%Y-%m-%d %H:%M')
        }
        
        return jsonify({
            'success': True,
            'duty': duty_info
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/delete-duty/<int:duty_id>', methods=['DELETE'])
@login_required
def delete_duty(duty_id):
    """حذف كشف واجب"""
    try:
        duty_data = DutyData.query.get_or_404(duty_id)
        
        # حذف أفراد الواجب أولاً
        DutyPersonnel.query.filter_by(duty_data_id=duty_id).delete()
        
        # حذف كشف الواجب
        db.session.delete(duty_data)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم حذف كشف الواجب بنجاح'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        })

# Routes لقوالب الواجبات

@duties_bp.route('/api/save-template', methods=['POST'])
@login_required
def save_template():
    """حفظ قالب واجب جديد"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'success': False, 'error': 'لا توجد بيانات'})

        # التحقق من البيانات المطلوبة
        required_fields = ['name', 'location_id', 'template_data']
        for field in required_fields:
            if field not in data:
                return jsonify({'success': False, 'error': f'حقل مطلوب مفقود: {field}'})

        # إنشاء قالب جديد
        template = DutyTemplate(
            name=data['name'],
            location_id=data['location_id'],
            template_data=json.dumps(data['template_data'], ensure_ascii=False),
            created_by=current_user.id
        )

        db.session.add(template)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حفظ القالب بنجاح',
            'template_id': template.id
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/get-templates/<int:location_id>', methods=['GET'])
@login_required
def get_templates(location_id):
    """الحصول على قوالب الواجبات لموقع معين"""
    try:
        templates = DutyTemplate.query.filter_by(
            location_id=location_id,
            is_active=True
        ).order_by(DutyTemplate.name).all()

        templates_data = []
        for template in templates:
            templates_data.append({
                'id': template.id,
                'name': template.name,
                'template_data': json.loads(template.template_data),
                'created_at': template.created_at.strftime('%Y-%m-%d %H:%M')
            })

        return jsonify({
            'success': True,
            'templates': templates_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/delete-template/<int:template_id>', methods=['DELETE'])
@login_required
def delete_template(template_id):
    """حذف قالب واجب"""
    try:
        template = DutyTemplate.query.get_or_404(template_id)

        # تعطيل القالب بدلاً من حذفه
        template.is_active = False
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حذف القالب بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/save-receipt', methods=['POST'])
@login_required
def save_receipt():
    """حفظ كشف الواجبات الكامل"""
    try:
        print("🔄 بدء عملية حفظ الكشف...")

        data = request.get_json()
        print(f"📋 البيانات المستلمة: {data is not None}")

        if not data:
            print("❌ لا توجد بيانات")
            return jsonify({'success': False, 'message': 'لا توجد بيانات'})

        print(f"👤 المستخدم الحالي: {current_user.id}")

        # الحصول على أول موقع متاح أو إنشاء موقع افتراضي
        first_location = Location.query.filter_by(status='نشط').first()
        if not first_location:
            # إنشاء موقع افتراضي إذا لم يوجد
            first_location = Location(
                name='موقع افتراضي',
                type='عام',
                serial_number='DEFAULT001',
                description='موقع افتراضي للكشوفات',
                status='نشط'
            )
            db.session.add(first_location)
            db.session.flush()  # للحصول على ID

        # إنشاء سجل كشف الواجب
        duty_data = DutyData(
            location_id=first_location.id,
            user_id=current_user.id,
            duty_date=datetime.now().date(),
            duty_time=datetime.now().time(),
            duty_data=json.dumps(data, ensure_ascii=False),
            notes=f"كشف واجبات - {data.get('day_name', '')}"
        )

        print("💾 إضافة البيانات إلى قاعدة البيانات...")
        db.session.add(duty_data)
        db.session.commit()

        print(f"✅ تم حفظ الكشف بنجاح - ID: {duty_data.id}")

        return jsonify({
            'success': True,
            'message': 'تم حفظ الكشف بنجاح',
            'duty_id': duty_data.id
        })

    except Exception as e:
        print(f"❌ خطأ في حفظ الكشف: {str(e)}")
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': str(e)
        })

@duties_bp.route('/api/save-draft', methods=['POST'])
@login_required
def save_draft():
    """حفظ مسودة كشف الواجب"""
    try:
        data = request.get_json()
        print(f"💾 حفظ مسودة: {len(str(data))} حرف")

        # البحث عن مسودة موجودة للمستخدم الحالي
        existing_draft = DutyTemplate.query.filter_by(
            name=f"مسودة_{current_user.id}",
            created_by=current_user.id
        ).first()

        if existing_draft:
            # تحديث المسودة الموجودة
            existing_draft.template_data = json.dumps(data, ensure_ascii=False)
            existing_draft.updated_at = get_saudi_now()
            print(f"✅ تم تحديث المسودة الموجودة")
        else:
            # إنشاء مسودة جديدة
            # نحتاج إلى موقع افتراضي للمسودات
            default_location = Location.query.filter_by(status='نشط').first()
            if not default_location:
                # إنشاء موقع افتراضي للمسودات
                default_location = Location(
                    name='مسودات',
                    type='نظام',
                    serial_number='DRAFT001',
                    description='موقع افتراضي للمسودات',
                    status='نشط'
                )
                db.session.add(default_location)
                db.session.flush()

            draft = DutyTemplate(
                name=f"مسودة_{current_user.id}",
                template_data=json.dumps(data, ensure_ascii=False),
                created_by=current_user.id,
                location_id=default_location.id
            )
            db.session.add(draft)
            print(f"✅ تم إنشاء مسودة جديدة")

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حفظ المسودة بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في حفظ المسودة: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/load-draft', methods=['GET'])
@login_required
def load_draft():
    """تحميل مسودة كشف الواجب"""
    try:
        # البحث عن مسودة المستخدم الحالي
        draft = DutyTemplate.query.filter_by(
            name=f"مسودة_{current_user.id}",
            created_by=current_user.id
        ).first()

        if draft:
            draft_data = json.loads(draft.template_data)
            print(f"✅ تم تحميل المسودة: {len(str(draft_data))} حرف")

            return jsonify({
                'success': True,
                'data': draft_data,
                'last_updated': draft.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        else:
            print(f"⚠️ لا توجد مسودة محفوظة للمستخدم {current_user.id}")
            return jsonify({
                'success': False,
                'message': 'لا توجد مسودة محفوظة'
            })

    except Exception as e:
        print(f"❌ خطأ في تحميل المسودة: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/get-personnel/<int:personnel_id>')
@login_required
def get_personnel_by_id(personnel_id):
    """الحصول على بيانات فرد واحد بالرقم"""
    try:
        personnel = Personnel.query.get(personnel_id)
        if personnel:
            return jsonify({
                'success': True,
                'personnel': {
                    'id': personnel.id,
                    'name': personnel.name,
                    'rank': personnel.rank,
                    'military_number': personnel.military_number,
                    'display_name': f"{personnel.name} ({personnel.rank})"
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': 'الفرد غير موجود'
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/api/clear-draft', methods=['POST'])
@login_required
def clear_draft():
    """مسح المسودة من قاعدة البيانات"""
    try:
        print(f"🗑️ مسح مسودة المستخدم {current_user.id}")

        # حذف المسودة الحالية للمستخدم
        draft = DutyTemplate.query.filter_by(user_id=current_user.id).first()
        if draft:
            db.session.delete(draft)
            db.session.commit()
            print("✅ تم مسح المسودة من قاعدة البيانات")
            return jsonify({'success': True, 'message': 'تم مسح المسودة'})
        else:
            print("⚠️ لا توجد مسودة لمسحها")
            return jsonify({'success': True, 'message': 'لا توجد مسودة لمسحها'})

    except Exception as e:
        print(f"❌ خطأ في مسح المسودة: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@duties_bp.route('/export-excel', methods=['POST'])
@login_required
def export_excel():
    """تصدير كشف الواجبات إلى Excel أو CSV"""
    try:
        print("📥 تم استلام طلب تصدير كشف الواجبات")
        data = request.get_json()
        print(f"📦 البيانات المستلمة: {data}")

        if not data:
            print("❌ لا توجد بيانات JSON")
            return jsonify({'success': False, 'message': 'لا توجد بيانات للتصدير'})

        # استخراج البيانات
        duty_info = data.get('duty_info', {})
        duty_table = data.get('duty_table', {})
        patrol_table = data.get('patrol_table', {})
        shifts_table = data.get('shifts_table', {})

        print(f"📋 معلومات الكشف: {duty_info}")
        print(f"📊 جدول الواجبات: {len(duty_table.get('rows', []))} صف")
        print(f"📊 جدول الدوريات: {len(patrol_table.get('rows', []))} صف")
        print(f"📊 جدول المناوبات: {len(shifts_table.get('rows', []))} صف")

        # إنشاء اسم الملف
        filename = f"كشف_الواجبات_{duty_info.get('gregorian_date', datetime.now().strftime('%Y-%m-%d'))}"

        # محاولة إنشاء ملف Excel أولاً
        if EXCEL_AVAILABLE:
            try:
                excel_file = create_duty_excel(duty_table, patrol_table, shifts_table, duty_info, filename)
                if excel_file:
                    print("✅ تم إنشاء ملف Excel بنجاح")
                    return send_file(
                        excel_file,
                        download_name=f"{filename}.xlsx",
                        as_attachment=True,
                        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    )
            except Exception as e:
                print(f"❌ فشل في إنشاء Excel: {str(e)}")

        # إنشاء ملف CSV كبديل
        print("📄 إنشاء ملف CSV كبديل...")
        csv_file = create_duty_csv(duty_table, patrol_table, shifts_table, duty_info, filename)

        if csv_file:
            print("✅ تم إنشاء ملف CSV بنجاح")
            return send_file(
                csv_file,
                download_name=f"{filename}.csv",
                as_attachment=True,
                mimetype='text/csv; charset=utf-8'
            )
        else:
            print("❌ فشل في إنشاء ملف CSV")
            return jsonify({'success': False, 'message': 'فشل في إنشاء الملف'})

    except Exception as e:
        print(f"❌ خطأ في التصدير: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'message': f'خطأ في التصدير: {str(e)}'})

def create_duty_excel(duty_table, patrol_table, shifts_table, duty_info, filename):
    """إنشاء ملف Excel لكشف الواجبات"""
    try:
        if not EXCEL_AVAILABLE:
            return None

        print(f"📊 بدء إنشاء ملف Excel: {filename}")

        # إنشاء buffer في الذاكرة
        output = BytesIO()

        # إنشاء workbook
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})

        # تنسيقات الخلايا (مطابقة لكشف الاستلامات)
        # تنسيق معلومات التاريخ في الأعلى
        date_header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'bg_color': '#FFFFFF',
            'border': 1,
            'font_size': 12,
            'font_name': 'Arial'
        })

        # تنسيق عناوين الجداول
        table_title_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'bg_color': '#D7E4BC',
            'border': 1,
            'font_size': 12,
            'font_name': 'Arial'
        })

        # تنسيق رؤوس الأعمدة
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'bg_color': '#D7E4BC',
            'border': 1,
            'font_size': 10,
            'font_name': 'Arial'
        })

        # تنسيق الخلايا العادية
        cell_format = workbook.add_format({
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'border': 1,
            'font_size': 10,
            'font_name': 'Arial'
        })

        # تنسيق خلايا الأرقام
        number_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'vcenter',
            'align': 'center',
            'bg_color': '#F0F0F0',
            'border': 1,
            'font_size': 10,
            'font_name': 'Arial'
        })

        # إنشاء ورقة العمل
        worksheet = workbook.add_worksheet('كشف الواجبات')
        worksheet.right_to_left()

        row = 0

        # ===== معلومات التاريخ في الأعلى (نفس تنسيق كشف الاستلامات) =====
        gregorian_date = duty_info.get('gregorian_date', '')
        hijri_date = duty_info.get('hijri_date', '')
        receipt_number = duty_info.get('receipt_number', '')
        day_name = duty_info.get('day_name', '')

        # الصف الأول: التاريخ الميلادي والتاريخ الهجري
        worksheet.write(row, 0, gregorian_date, date_header_format)
        worksheet.write(row, 1, 'التاريخ الميلادي', date_header_format)
        worksheet.write(row, 2, hijri_date, date_header_format)
        worksheet.write(row, 3, 'التاريخ الهجري', date_header_format)

        # الصف الثاني: رقم الكشف واليوم
        row += 1
        worksheet.write(row, 0, receipt_number, date_header_format)
        worksheet.write(row, 1, 'رقم الكشف', date_header_format)
        worksheet.write(row, 2, day_name, date_header_format)
        worksheet.write(row, 3, 'اليوم', date_header_format)

        row += 2  # مسافة بين المعلومات والجداول

        # ===== قسم كشف الواجبات الرئيسي =====
        if duty_table.get('headers') and duty_table.get('rows'):
            # عنوان كشف الواجبات
            max_cols = max(len(duty_table['headers']), 8)
            worksheet.merge_range(row, 0, row, max_cols - 1,
                                'كشف الواجبات الرئيسي', table_title_format)
            row += 1

            # كتابة بيانات كشف الواجبات
            # العناوين
            for col, header in enumerate(duty_table['headers']):
                worksheet.write(row, col, header, header_format)
                worksheet.set_column(col, col, 15)
            row += 1

            # البيانات
            for row_idx, duty_row in enumerate(duty_table['rows']):
                for col, cell_data in enumerate(duty_row):
                    # تنسيق خاص لعمود الرقم
                    if col == 0:
                        worksheet.write(row, col, cell_data or '', number_format)
                    else:
                        worksheet.write(row, col, cell_data or '', cell_format)
                row += 1
            row += 2

        # ===== قسم كشف الدوريات =====
        if patrol_table.get('headers') and patrol_table.get('rows'):
            # عنوان كشف الدوريات
            max_cols = max(len(patrol_table['headers']), 8)
            worksheet.merge_range(row, 0, row, max_cols - 1,
                                'كشف الدوريات', table_title_format)
            row += 1

            # العناوين
            for col, header in enumerate(patrol_table['headers']):
                worksheet.write(row, col, header, header_format)
            row += 1

            # البيانات
            for row_idx, patrol_row in enumerate(patrol_table['rows']):
                for col, cell_data in enumerate(patrol_row):
                    # تنسيق خاص لعمود الرقم
                    if col == 0:
                        worksheet.write(row, col, cell_data or '', number_format)
                    else:
                        worksheet.write(row, col, cell_data or '', cell_format)
                row += 1
            row += 2

        # ===== قسم كشف المناوبات =====
        if shifts_table.get('headers') and shifts_table.get('rows'):
            # عنوان كشف المناوبات
            max_cols = max(len(shifts_table['headers']), 8)
            worksheet.merge_range(row, 0, row, max_cols - 1,
                                'كشف المناوبات', table_title_format)
            row += 1

            # العناوين
            for col, header in enumerate(shifts_table['headers']):
                worksheet.write(row, col, header, header_format)
            row += 1

            # البيانات
            for row_idx, shifts_row in enumerate(shifts_table['rows']):
                for col, cell_data in enumerate(shifts_row):
                    # تنسيق خاص لعمود الرقم
                    if col == 0:
                        worksheet.write(row, col, cell_data or '', number_format)
                    else:
                        worksheet.write(row, col, cell_data or '', cell_format)
                row += 1

        # إعدادات الطباعة
        worksheet.set_landscape()
        worksheet.set_paper(9)  # A4
        worksheet.fit_to_pages(1, 0)
        worksheet.set_margins(0.3, 0.3, 0.5, 0.5)

        # إغلاق الـ workbook
        workbook.close()
        output.seek(0)

        print(f"✅ تم إنشاء ملف Excel بنجاح، الحجم: {len(output.getvalue())} بايت")
        return output

    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف Excel: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def create_duty_csv(duty_table, patrol_table, shifts_table, duty_info, filename):
    """إنشاء ملف CSV كبديل"""
    try:
        print(f"📄 بدء إنشاء ملف CSV: {filename}")

        # إنشاء ملف مؤقت
        temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.csv', encoding='utf-8-sig')

        writer = csv.writer(temp_file)

        # كتابة معلومات الكشف
        writer.writerow(['كشف الواجبات - الفريق الأمني'])
        writer.writerow(['=' * 60])
        writer.writerow([])

        if duty_info.get('day_name'):
            writer.writerow(['اليوم:', duty_info['day_name']])

        if duty_info.get('hijri_date'):
            writer.writerow(['التاريخ الهجري:', duty_info['hijri_date']])

        if duty_info.get('gregorian_date'):
            writer.writerow(['التاريخ الميلادي:', duty_info['gregorian_date']])

        if duty_info.get('receipt_number'):
            writer.writerow(['رقم الكشف:', duty_info['receipt_number']])

        writer.writerow([])

        # كتابة جدول الواجبات الرئيسي
        if duty_table.get('headers') and duty_table.get('rows'):
            writer.writerow(['كشف الواجبات الرئيسي'])
            writer.writerow(['-' * 40])
            writer.writerow(duty_table['headers'])

            for row in duty_table['rows']:
                writer.writerow([cell or '' for cell in row])

            writer.writerow([])

        # كتابة جدول الدوريات
        if patrol_table.get('headers') and patrol_table.get('rows'):
            writer.writerow(['كشف الدوريات'])
            writer.writerow(['-' * 40])
            writer.writerow(patrol_table['headers'])

            for row in patrol_table['rows']:
                writer.writerow([cell or '' for cell in row])

            writer.writerow([])

        # كتابة جدول المناوبات
        if shifts_table.get('headers') and shifts_table.get('rows'):
            writer.writerow(['كشف المناوبات'])
            writer.writerow(['-' * 40])
            writer.writerow(shifts_table['headers'])

            for row in shifts_table['rows']:
                writer.writerow([cell or '' for cell in row])

        temp_file.close()

        print(f"✅ تم إنشاء ملف CSV بنجاح: {temp_file.name}")
        return temp_file.name

    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف CSV: {str(e)}")
        return None
