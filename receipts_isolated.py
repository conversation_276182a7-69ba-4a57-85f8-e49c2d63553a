# كشف الاستلامات المنفصل - Backend API
from flask import Blueprint, render_template, request, jsonify, session, current_app
from flask_login import login_required, current_user
import json
import os
from datetime import datetime, date
from models import db, ReceiptIsolatedMainData, ReceiptIsolatedPatrolData, ReceiptIsolatedShiftsData
from auth_utils import admin_required
from datetime_utils import get_saudi_now

# إنشاء Blueprint منفصل لكشف الاستلامات
receipts_isolated_bp = Blueprint('receipts_isolated', __name__, url_prefix='/receipts-isolated')

@receipts_isolated_bp.route('/')
@login_required
def index():
    """صفحة كشف الاستلامات المنفصلة"""
    return render_template('receipts/isolated_index.html')

@receipts_isolated_bp.route('/api/save-main-data', methods=['POST'])
@login_required
def save_main_data():
    """حفظ البيانات الرئيسية لكشف الاستلامات"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'message': 'لا توجد بيانات للحفظ'})
        
        # البحث عن سجل موجود للمستخدم الحالي واليوم الحالي
        today = date.today()
        existing_receipt = ReceiptIsolatedMainData.query.filter_by(
            user_id=current_user.id,
            receipt_date=today
        ).first()

        if existing_receipt:
            # تحديث السجل الموجود
            existing_receipt.receipt_data = json.dumps(data, ensure_ascii=False)
            existing_receipt.updated_at = get_saudi_now()
            print(f"✅ تم تحديث بيانات كشف الاستلامات الموجودة")
        else:
            # إنشاء سجل جديد
            receipt_data = ReceiptIsolatedMainData(
                user_id=current_user.id,
                receipt_date=today,
                receipt_data=json.dumps(data, ensure_ascii=False),
                notes=f"كشف استلامات - {data.get('hijriDate', '')}"
            )
            db.session.add(receipt_data)
            print(f"✅ تم إنشاء سجل جديد لكشف الاستلامات")
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم حفظ بيانات كشف الاستلامات بنجاح'
        })
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في حفظ بيانات كشف الاستلامات: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في حفظ البيانات: {str(e)}'
        })

@receipts_isolated_bp.route('/api/save-patrol-data', methods=['POST'])
@login_required
def save_patrol_data():
    """حفظ بيانات دوريات الاستلامات"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'message': 'لا توجد بيانات دوريات للحفظ'})
        
        # البحث عن سجل موجود
        today = date.today()
        existing_patrol = ReceiptIsolatedPatrolData.query.filter_by(
            user_id=current_user.id,
            patrol_date=today
        ).first()

        if existing_patrol:
            # تحديث السجل الموجود
            existing_patrol.patrol_data = json.dumps(data, ensure_ascii=False)
            existing_patrol.updated_at = get_saudi_now()
        else:
            # إنشاء سجل جديد
            patrol_data = ReceiptIsolatedPatrolData(
                user_id=current_user.id,
                patrol_date=today,
                patrol_data=json.dumps(data, ensure_ascii=False)
            )
            db.session.add(patrol_data)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم حفظ بيانات دوريات الاستلامات بنجاح'
        })
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في حفظ بيانات دوريات الاستلامات: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في حفظ بيانات الدوريات: {str(e)}'
        })

@receipts_isolated_bp.route('/api/save-shifts-data', methods=['POST'])
@login_required
def save_shifts_data():
    """حفظ بيانات مناوبين الاستلامات"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'message': 'لا توجد بيانات مناوبين للحفظ'})
        
        # البحث عن سجل موجود
        today = date.today()
        existing_shifts = ReceiptIsolatedShiftsData.query.filter_by(
            user_id=current_user.id,
            shifts_date=today
        ).first()

        if existing_shifts:
            # تحديث السجل الموجود
            existing_shifts.shifts_data = json.dumps(data, ensure_ascii=False)
            existing_shifts.updated_at = get_saudi_now()
        else:
            # إنشاء سجل جديد
            shifts_data = ReceiptIsolatedShiftsData(
                user_id=current_user.id,
                shifts_date=today,
                shifts_data=json.dumps(data, ensure_ascii=False)
            )
            db.session.add(shifts_data)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم حفظ بيانات مناوبين الاستلامات بنجاح'
        })
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في حفظ بيانات مناوبين الاستلامات: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في حفظ بيانات المناوبين: {str(e)}'
        })

@receipts_isolated_bp.route('/api/get-main-data', methods=['GET'])
@login_required
def get_main_data():
    """تحميل البيانات الرئيسية لكشف الاستلامات"""
    try:
        today = date.today()
        receipt_data = ReceiptIsolatedMainData.query.filter_by(
            user_id=current_user.id,
            receipt_date=today
        ).first()
        
        if receipt_data:
            data = json.loads(receipt_data.receipt_data)
            return jsonify({
                'success': True,
                'data': data
            })
        else:
            return jsonify({
                'success': False,
                'message': 'لا توجد بيانات محفوظة'
            })
            
    except Exception as e:
        print(f"❌ خطأ في تحميل بيانات كشف الاستلامات: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في تحميل البيانات: {str(e)}'
        })

@receipts_isolated_bp.route('/api/get-patrol-data', methods=['GET'])
@login_required
def get_patrol_data():
    """تحميل بيانات دوريات الاستلامات"""
    try:
        today = date.today()
        patrol_data = ReceiptIsolatedPatrolData.query.filter_by(
            user_id=current_user.id,
            patrol_date=today
        ).first()
        
        if patrol_data:
            data = json.loads(patrol_data.patrol_data)
            return jsonify({
                'success': True,
                'data': data
            })
        else:
            return jsonify({
                'success': False,
                'message': 'لا توجد بيانات دوريات محفوظة'
            })
            
    except Exception as e:
        print(f"❌ خطأ في تحميل بيانات دوريات الاستلامات: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في تحميل بيانات الدوريات: {str(e)}'
        })

@receipts_isolated_bp.route('/api/get-shifts-data', methods=['GET'])
@login_required
def get_shifts_data():
    """تحميل بيانات مناوبين الاستلامات"""
    try:
        today = date.today()
        shifts_data = ReceiptIsolatedShiftsData.query.filter_by(
            user_id=current_user.id,
            shifts_date=today
        ).first()
        
        if shifts_data:
            data = json.loads(shifts_data.shifts_data)
            return jsonify({
                'success': True,
                'shiftsData': data
            })
        else:
            return jsonify({
                'success': False,
                'message': 'لا توجد بيانات مناوبين محفوظة'
            })
            
    except Exception as e:
        print(f"❌ خطأ في تحميل بيانات مناوبين الاستلامات: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في تحميل بيانات المناوبين: {str(e)}'
        })

@receipts_isolated_bp.route('/api/clear-all-data', methods=['POST'])
@login_required
def clear_all_data():
    """مسح جميع بيانات كشف الاستلامات"""
    try:
        today = date.today()
        
        # مسح البيانات الرئيسية
        ReceiptIsolatedMainData.query.filter_by(
            user_id=current_user.id,
            receipt_date=today
        ).delete()

        # مسح بيانات الدوريات
        ReceiptIsolatedPatrolData.query.filter_by(
            user_id=current_user.id,
            patrol_date=today
        ).delete()

        # مسح بيانات المناوبين
        ReceiptIsolatedShiftsData.query.filter_by(
            user_id=current_user.id,
            shifts_date=today
        ).delete()
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم مسح جميع بيانات كشف الاستلامات بنجاح'
        })
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ خطأ في مسح بيانات كشف الاستلامات: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'خطأ في مسح البيانات: {str(e)}'
        })

print("✅ تم تحميل ملف receipts_isolated.py بنجاح")
