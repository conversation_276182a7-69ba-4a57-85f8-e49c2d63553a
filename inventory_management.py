from datetime import datetime
from datetime_utils import get_saudi_now
from flask import Blueprint, request, render_template, flash, redirect, url_for, jsonify
from flask_login import login_required, current_user
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, TextAreaField, IntegerField, DecimalField, DateTimeField, SubmitField
from wtforms.validators import DataRequired, Length, Optional, NumberRange
from sqlalchemy import or_, and_

from db import db
from models import InventoryItem, InventoryTransaction, Warehouse, User, ActivityLog

# إنشاء Blueprint للمخزون
inventory_bp = Blueprint('inventory_management', __name__, url_prefix='/inventory_management')

# نماذج الفئات والفئات الفرعية
CATEGORIES = {
    'ملبوسات عسكرية': [
        'بدلة قتالية', 'قميص عسكري', 'بنطلون عسكري', 'جاكيت عسكري',
        'حذاء عسكري', 'جوارب عسكرية', 'قبعة عسكرية', 'حزام عسكري',
        'قفازات عسكرية', 'ملابس داخلية عسكرية', 'معطف مطر', 'سترة واقية'
    ],
    'معدات عسكرية': [
        'خوذة واقية', 'سترة واقية من الرصاص', 'حقيبة ظهر عسكرية', 'حقيبة معدات',
        'منظار ليلي', 'منظار عادي', 'جهاز لاسلكي', 'بوصلة', 'خريطة',
        'مصباح يدوي', 'بطارية', 'كابل', 'أدوات صيانة', 'معدات طبية',
        'خيمة', 'كيس نوم', 'أدوات طبخ', 'قارورة ماء'
    ],
    'ذخيرة': [
        'رصاص بندقية', 'رصاص مسدس', 'قنابل يدوية', 'صواريخ',
        'متفجرات', 'قذائف مدفعية', 'ألغام', 'مواد متفجرة'
    ]
}

STATUSES = [
    ('متوفر', 'متوفر'),
    ('مُصدر', 'مُصدر'),
    ('صيانة', 'صيانة'),
    ('تالف', 'تالف'),
    ('منتهي الصلاحية', 'منتهي الصلاحية'),
    ('محجوز', 'محجوز')
]

TRANSACTION_TYPES = [
    ('receive', 'استلام'),
    ('issue', 'صرف'),
    ('return', 'إرجاع'),
    ('transfer', 'نقل'),
    ('adjustment', 'تعديل')
]

# نموذج مبسط لإضافة الأصناف
class SimpleInventoryItemForm(FlaskForm):
    """نموذج مبسط لإضافة صنف مخزون"""
    item_code = StringField('كود الصنف', validators=[DataRequired(), Length(max=100)])
    name = StringField('اسم الصنف', validators=[DataRequired(), Length(max=200)])
    category = SelectField('الفئة', choices=[
        ('ملبوسات', 'ملبوسات'),
        ('معدات', 'معدات'),
        ('ذخيرة', 'ذخيرة')
    ], validators=[DataRequired()])
    subcategory = StringField('الفئة الفرعية', validators=[Optional(), Length(max=100)])
    size = StringField('المقاس', validators=[Optional(), Length(max=50)])
    color = StringField('اللون', validators=[Optional(), Length(max=50)])
    quantity_in_stock = IntegerField('الكمية المتوفرة', validators=[DataRequired(), NumberRange(min=0)], default=0)
    notes = TextAreaField('ملاحظات', validators=[Optional()])
    submit = SubmitField('إضافة الصنف')

# النموذج الكامل للتعديل
class InventoryItemForm(FlaskForm):
    """نموذج كامل لتعديل صنف مخزون"""
    item_code = StringField('كود الصنف', validators=[DataRequired(), Length(max=100)])
    name = StringField('اسم الصنف', validators=[DataRequired(), Length(max=200)])
    category = SelectField('الفئة', choices=[
        ('ملبوسات', 'ملبوسات'),
        ('معدات', 'معدات'),
        ('ذخيرة', 'ذخيرة')
    ], validators=[DataRequired()])
    subcategory = StringField('الفئة الفرعية', validators=[Optional(), Length(max=100)])
    brand = StringField('الماركة/الشركة المصنعة', validators=[Optional(), Length(max=100)])
    model = StringField('الموديل', validators=[Optional(), Length(max=100)])
    size = StringField('المقاس', validators=[Optional(), Length(max=50)])
    color = StringField('اللون', validators=[Optional(), Length(max=50)])
    material = StringField('المادة', validators=[Optional(), Length(max=100)])

    # معلومات الكمية
    quantity_in_stock = IntegerField('الكمية المتوفرة', validators=[DataRequired(), NumberRange(min=0)], default=0)
    minimum_stock = IntegerField('الحد الأدنى للمخزون', validators=[Optional(), NumberRange(min=0)], default=0)
    maximum_stock = IntegerField('الحد الأقصى للمخزون', validators=[Optional(), NumberRange(min=0)], default=0)

    # معلومات التكلفة
    unit_cost = DecimalField('تكلفة الوحدة', validators=[Optional(), NumberRange(min=0)], places=2)

    # معلومات التواريخ
    manufacture_date = DateTimeField('تاريخ التصنيع', validators=[Optional()], format='%Y-%m-%d')
    expiry_date = DateTimeField('تاريخ انتهاء الصلاحية', validators=[Optional()], format='%Y-%m-%d')
    purchase_date = DateTimeField('تاريخ الشراء', validators=[Optional()], format='%Y-%m-%d')

    # معلومات الموقع
    status = SelectField('الحالة', choices=STATUSES, validators=[DataRequired()])
    location = StringField('الموقع في المستودع', validators=[Optional(), Length(max=200)])
    shelf_number = StringField('رقم الرف', validators=[Optional(), Length(max=50)])

    # معلومات إضافية
    supplier = StringField('المورد', validators=[Optional(), Length(max=200)])
    batch_number = StringField('رقم الدفعة', validators=[Optional(), Length(max=100)])
    serial_numbers = TextAreaField('الأرقام التسلسلية', validators=[Optional()])
    specifications = TextAreaField('المواصفات التقنية', validators=[Optional()])
    notes = TextAreaField('ملاحظات', validators=[Optional()])

    warehouse_id = SelectField('المستودع', coerce=int, validators=[DataRequired()])

    submit = SubmitField('حفظ')

class InventoryTransactionForm(FlaskForm):
    """نموذج معاملة مخزون"""
    item_id = SelectField('الصنف', coerce=int, validators=[DataRequired()])
    transaction_type = SelectField('نوع المعاملة', choices=TRANSACTION_TYPES, validators=[DataRequired()])
    quantity = IntegerField('الكمية', validators=[DataRequired(), NumberRange(min=1)])
    
    # معلومات المستلم
    recipient_name = StringField('اسم المستلم', validators=[Optional(), Length(max=200)])
    recipient_id = StringField('رقم هوية المستلم', validators=[Optional(), Length(max=100)])
    recipient_rank = StringField('رتبة المستلم', validators=[Optional(), Length(max=50)])
    recipient_unit = StringField('وحدة المستلم', validators=[Optional(), Length(max=200)])
    
    # معلومات التحويل
    from_warehouse_id = SelectField('من المستودع', coerce=int, validators=[Optional()])
    to_warehouse_id = SelectField('إلى المستودع', coerce=int, validators=[Optional()])
    
    # معلومات إضافية
    reference_number = StringField('رقم المرجع', validators=[Optional(), Length(max=100)])
    reason = StringField('السبب', validators=[Optional(), Length(max=200)])
    notes = TextAreaField('ملاحظات', validators=[Optional()])
    approved_by = StringField('معتمد من', validators=[Optional(), Length(max=200)])
    
    submit = SubmitField('تنفيذ المعاملة')

def log_activity(action, details, warehouse_id=None):
    """تسجيل نشاط في سجل الأنشطة"""
    try:
        activity = ActivityLog(
            user_id=current_user.id,
            action=action,
            details=details,
            warehouse_id=warehouse_id,
            timestamp=get_saudi_now()
        )
        db.session.add(activity)
        db.session.commit()
    except Exception as e:
        print(f"Error logging activity: {e}")

@inventory_bp.route('/')
@login_required
def index():
    """صفحة المخزون الرئيسية - مستقلة عن المستودعات"""

    # إحصائيات المخزون العامة
    total_items = InventoryItem.query.count()
    low_stock_items = InventoryItem.query.filter(
        InventoryItem.quantity_in_stock <= InventoryItem.minimum_stock
    ).count()

    # الأصناف منتهية الصلاحية أو قريبة من انتهاء الصلاحية
    current_date = get_saudi_now()
    expired_items = InventoryItem.query.filter(
        InventoryItem.expiry_date < current_date
    ).count()

    # إحصائيات حسب الفئة
    categories_stats = {}
    for category in ['ملبوسات', 'معدات', 'ذخيرة']:
        count = InventoryItem.query.filter(InventoryItem.category == category).count()
        categories_stats[category] = count

    return render_template('inventory/index.html',
                         total_items=total_items,
                         low_stock_items=low_stock_items,
                         expired_items=expired_items,
                         categories_stats=categories_stats)

@inventory_bp.route('/items')
@login_required
def items():
    """قائمة أصناف المخزون - مع دعم البحث بالباركود"""

    # فلاتر البحث
    search = request.args.get('search', '')
    category = request.args.get('category', '')
    status = request.args.get('status', '')

    # بناء الاستعلام
    query = InventoryItem.query

    if search:
        # البحث في الاسم، الكود، الباركود، أو أي معرف آخر
        query = query.filter(or_(
            InventoryItem.name.contains(search),
            InventoryItem.item_code.contains(search),
            InventoryItem.item_code == search,  # بحث دقيق للباركود
            InventoryItem.subcategory.contains(search),
            InventoryItem.notes.contains(search)
        ))

    if category:
        query = query.filter(InventoryItem.category == category)

    if status:
        query = query.filter(InventoryItem.status == status)

    # ترتيب النتائج
    items = query.order_by(InventoryItem.name).all()

    return render_template('inventory/items.html',
                         items=items,
                         search=search,
                         category=category,
                         status=status,
                         categories=['ملبوسات', 'معدات', 'ذخيرة'],
                         statuses=[s[0] for s in STATUSES])

@inventory_bp.route('/items/add', methods=['GET', 'POST'])
@login_required
def add_item():
    """إضافة صنف جديد للمخزون - نموذج مبسط"""
    form = SimpleInventoryItemForm()

    # تحديد الفئة مسبقاً إذا تم تمريرها في الرابط
    category = request.args.get('category')
    if category and request.method == 'GET':
        form.category.data = category

    if form.validate_on_submit():
        try:
            # الحصول على المستودع الافتراضي (أول مستودع متاح)
            default_warehouse = Warehouse.query.first()
            if not default_warehouse:
                flash('لا يوجد مستودع متاح في النظام', 'error')
                return render_template('inventory/add_item_simple.html', form=form)

            # إنشاء الصنف الجديد بالحقول الأساسية فقط
            item = InventoryItem(
                item_code=form.item_code.data,
                name=form.name.data,
                category=form.category.data,
                subcategory=form.subcategory.data,
                size=form.size.data,
                color=form.color.data,
                quantity_in_stock=form.quantity_in_stock.data,
                quantity_issued=0,
                minimum_stock=0,
                maximum_stock=0,
                status='متوفر',
                notes=form.notes.data,
                warehouse_id=default_warehouse.id
            )

            db.session.add(item)
            db.session.commit()

            # تسجيل النشاط
            log_activity(
                'إضافة صنف مخزون',
                f'تم إضافة الصنف: {item.name} (كود: {item.item_code})',
                item.warehouse_id
            )

            flash(f'تم إضافة الصنف "{item.name}" بنجاح', 'success')
            return redirect(url_for('inventory_management.items'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة الصنف: {str(e)}', 'error')

    return render_template('inventory/add_item_simple.html', form=form)

@inventory_bp.route('/items/<int:item_id>')
@login_required
def item_details(item_id):
    """تفاصيل صنف مخزون"""
    item = InventoryItem.query.get_or_404(item_id)

    # التحقق من صلاحية الوصول
    if current_user.role != 'admin' and item.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية للوصول لهذا الصنف', 'error')
        return redirect(url_for('inventory.items'))

    # جلب آخر المعاملات
    recent_transactions = InventoryTransaction.query.filter_by(item_id=item_id)\
        .order_by(InventoryTransaction.transaction_date.desc()).limit(10).all()

    return render_template('inventory/item_details.html',
                         item=item,
                         recent_transactions=recent_transactions)

@inventory_bp.route('/items/<int:item_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_item(item_id):
    """تعديل صنف مخزون"""
    item = InventoryItem.query.get_or_404(item_id)

    # التحقق من صلاحية الوصول
    if current_user.role != 'admin' and item.warehouse not in current_user.warehouses:
        flash('ليس لديك صلاحية لتعديل هذا الصنف', 'error')
        return redirect(url_for('inventory.items'))

    form = InventoryItemForm(obj=item)

    # تحديد المستودعات المتاحة
    if current_user.role == 'admin':
        warehouses = Warehouse.query.all()
    else:
        warehouses = current_user.warehouses

    form.warehouse_id.choices = [(w.id, w.name) for w in warehouses]

    if form.validate_on_submit():
        try:
            # حفظ البيانات القديمة للمقارنة
            old_data = {
                'name': item.name,
                'quantity_in_stock': item.quantity_in_stock,
                'status': item.status
            }

            # تحديث البيانات
            form.populate_obj(item)

            # حساب القيمة الإجمالية
            if item.unit_cost and item.quantity_in_stock:
                item.total_value = item.unit_cost * item.quantity_in_stock

            item.updated_at = get_saudi_now()
            db.session.commit()

            # تسجيل النشاط
            changes = []
            if old_data['name'] != item.name:
                changes.append(f"الاسم: {old_data['name']} → {item.name}")
            if old_data['quantity_in_stock'] != item.quantity_in_stock:
                changes.append(f"الكمية: {old_data['quantity_in_stock']} → {item.quantity_in_stock}")
            if old_data['status'] != item.status:
                changes.append(f"الحالة: {old_data['status']} → {item.status}")

            if changes:
                log_activity(
                    'تعديل صنف مخزون',
                    f'تم تعديل الصنف: {item.name} - التغييرات: {", ".join(changes)}',
                    item.warehouse_id
                )

            flash(f'تم تحديث الصنف "{item.name}" بنجاح', 'success')
            return redirect(url_for('inventory.item_details', item_id=item.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الصنف: {str(e)}', 'error')

    return render_template('inventory/edit_item.html', form=form, item=item, categories=CATEGORIES)

@inventory_bp.route('/transactions')
@login_required
def transactions():
    """قائمة معاملات المخزون"""
    # فلترة حسب المستودعات المسموحة للمستخدم
    if current_user.role == 'admin':
        warehouses = Warehouse.query.all()
    else:
        warehouses = current_user.warehouses

    warehouse_ids = [w.id for w in warehouses]

    # فلاتر البحث
    transaction_type = request.args.get('transaction_type', '')
    warehouse_id = request.args.get('warehouse_id', type=int)

    # بناء الاستعلام
    query = InventoryTransaction.query.join(InventoryItem)\
        .filter(InventoryItem.warehouse_id.in_(warehouse_ids))

    if transaction_type:
        query = query.filter(InventoryTransaction.transaction_type == transaction_type)

    if warehouse_id and warehouse_id in warehouse_ids:
        query = query.filter(InventoryItem.warehouse_id == warehouse_id)

    # ترتيب النتائج
    transactions = query.order_by(InventoryTransaction.transaction_date.desc()).limit(100).all()

    return render_template('inventory/transactions.html',
                         transactions=transactions,
                         warehouses=warehouses,
                         transaction_type=transaction_type,
                         warehouse_id=warehouse_id,
                         transaction_types=TRANSACTION_TYPES)

@inventory_bp.route('/transactions/add', methods=['GET', 'POST'])
@login_required
def add_transaction():
    """إضافة معاملة مخزون جديدة"""
    form = InventoryTransactionForm()

    # تحديد المستودعات المتاحة
    if current_user.role == 'admin':
        warehouses = Warehouse.query.all()
    else:
        warehouses = current_user.warehouses

    warehouse_ids = [w.id for w in warehouses]

    # تحديد الأصناف المتاحة
    items = InventoryItem.query.filter(InventoryItem.warehouse_id.in_(warehouse_ids)).all()
    form.item_id.choices = [(i.id, f"{i.name} ({i.item_code})") for i in items]

    # تحديد المستودعات للتحويل
    form.from_warehouse_id.choices = [(0, 'اختر المستودع')] + [(w.id, w.name) for w in warehouses]
    form.to_warehouse_id.choices = [(0, 'اختر المستودع')] + [(w.id, w.name) for w in warehouses]

    if form.validate_on_submit():
        try:
            item = InventoryItem.query.get(form.item_id.data)
            if not item:
                flash('الصنف المحدد غير موجود', 'error')
                return render_template('inventory/add_transaction.html', form=form)

            # التحقق من توفر الكمية للصرف
            if form.transaction_type.data in ['issue', 'transfer'] and item.quantity_in_stock < form.quantity.data:
                flash(f'الكمية المتوفرة غير كافية. المتوفر: {item.quantity_in_stock}', 'error')
                return render_template('inventory/add_transaction.html', form=form)

            # حساب التكلفة
            unit_cost = item.unit_cost or 0
            total_cost = unit_cost * form.quantity.data

            # إنشاء المعاملة
            transaction = InventoryTransaction(
                item_id=form.item_id.data,
                transaction_type=form.transaction_type.data,
                quantity=form.quantity.data,
                unit_cost=unit_cost,
                total_cost=total_cost,
                reference_number=form.reference_number.data,
                recipient_name=form.recipient_name.data,
                recipient_id=form.recipient_id.data,
                recipient_rank=form.recipient_rank.data,
                recipient_unit=form.recipient_unit.data,
                from_warehouse_id=form.from_warehouse_id.data if form.from_warehouse_id.data else None,
                to_warehouse_id=form.to_warehouse_id.data if form.to_warehouse_id.data else None,
                reason=form.reason.data,
                notes=form.notes.data,
                approved_by=form.approved_by.data,
                user_id=current_user.id
            )

            # تحديث كميات المخزون
            if form.transaction_type.data == 'receive':
                item.quantity_in_stock += form.quantity.data
            elif form.transaction_type.data == 'issue':
                item.quantity_in_stock -= form.quantity.data
                item.quantity_issued += form.quantity.data
            elif form.transaction_type.data == 'return':
                item.quantity_in_stock += form.quantity.data
                item.quantity_issued = max(0, item.quantity_issued - form.quantity.data)
            elif form.transaction_type.data == 'transfer':
                item.quantity_in_stock -= form.quantity.data
            elif form.transaction_type.data == 'adjustment':
                # تعديل الكمية (يمكن أن يكون موجب أو سالب)
                item.quantity_in_stock = form.quantity.data

            # تحديث القيمة الإجمالية
            if item.unit_cost:
                item.total_value = item.unit_cost * item.quantity_in_stock

            item.updated_at = get_saudi_now()

            db.session.add(transaction)
            db.session.commit()

            # تسجيل النشاط
            transaction_names = dict(TRANSACTION_TYPES)
            log_activity(
                'معاملة مخزون',
                f'{transaction_names.get(form.transaction_type.data, form.transaction_type.data)}: {item.name} - الكمية: {form.quantity.data}',
                item.warehouse_id
            )

            flash(f'تم تنفيذ المعاملة بنجاح', 'success')
            return redirect(url_for('inventory.transactions'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تنفيذ المعاملة: {str(e)}', 'error')

    return render_template('inventory/add_transaction.html', form=form)

@inventory_bp.route('/reports')
@login_required
def reports():
    """تقارير المخزون"""
    # فلترة حسب المستودعات المسموحة للمستخدم
    if current_user.role == 'admin':
        warehouses = Warehouse.query.all()
    else:
        warehouses = current_user.warehouses

    warehouse_ids = [w.id for w in warehouses]

    # تقرير الأصناف منخفضة المخزون
    low_stock_items = InventoryItem.query.filter(
        and_(
            InventoryItem.warehouse_id.in_(warehouse_ids),
            InventoryItem.quantity_in_stock <= InventoryItem.minimum_stock
        )
    ).all()

    # تقرير الأصناف منتهية الصلاحية
    current_date = get_saudi_now()
    expired_items = InventoryItem.query.filter(
        and_(
            InventoryItem.warehouse_id.in_(warehouse_ids),
            InventoryItem.expiry_date < current_date
        )
    ).all()

    # تقرير القيمة الإجمالية للمخزون
    total_value = db.session.query(db.func.sum(InventoryItem.total_value))\
        .filter(InventoryItem.warehouse_id.in_(warehouse_ids)).scalar() or 0

    return render_template('inventory/reports.html',
                         low_stock_items=low_stock_items,
                         expired_items=expired_items,
                         total_value=total_value,
                         warehouses=warehouses)

@inventory_bp.route('/api/subcategories/<category>')
@login_required
def get_subcategories(category):
    """API لجلب الفئات الفرعية حسب الفئة الرئيسية"""
    subcategories = CATEGORIES.get(category, [])
    return jsonify(subcategories)
