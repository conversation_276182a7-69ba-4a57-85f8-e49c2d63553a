// كشف الواجبات - نسخة مبسطة
console.log('🚀 تحميل ملف duties-simple.js...');

// العناوين الافتراضية
const DEFAULT_HEADERS = ['الرقم', 'موقع الواجب', 'من 6 مساءً إلى 10 ليلاً', 'من 10 ليلاً إلى 2 ليلاً', 'من 2 ليلاً إلى 6 صباحاً', 'من 6 صباحاً إلى 10 صباحاً', 'من 10 صباحاً إلى 2 ظهراً', 'من 2 ظهراً إلى 6 مساءً', 'ملاحظات'];
const DEFAULT_PATROL_HEADERS = ['الرقم', 'موقع الواجب', '12 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 12 ظهراً', '12 ظهراً إلى 6 مساءً', 'ملاحظات'];
const DEFAULT_SHIFTS_HEADERS = ['الرقم', 'موقع الواجب', '2 ظهراً إلى 10 ليلاً', '10 ليلاً إلى 6 صباحاً', '6 صباحاً إلى 2 ظهراً', 'ملاحظات المناوبين'];

// بيانات الجداول
let dutyData = {
    headers: [...DEFAULT_HEADERS],
    rows: []
};

let patrolData = {
    headers: [...DEFAULT_PATROL_HEADERS],
    rows: []
};

let shiftsData = {
    headers: [...DEFAULT_SHIFTS_HEADERS],
    rows: []
};

// قاعدة بيانات المواقع والأفراد
let locationsDatabase = [];
let locationPersonnelMap = {};

// تحميل المواقع
async function loadLocations() {
    try {
        const response = await fetch('/duties/api/get-locations');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                locationsDatabase = data.locations;
                console.log(`✅ تم تحميل ${data.locations.length} موقع`);
            }
        }



    } catch (error) {
        console.error('❌ خطأ في تحميل المواقع:', error);
    }
}

// تحميل أفراد الموقع
async function loadPersonnelForLocation(locationId, rowIndex) {
    console.log(`🔄 تحميل أفراد الموقع ${locationId} للصف ${rowIndex}`);

    if (!locationId) {
        console.log('⚠️ لا يوجد معرف موقع');
        clearPersonnelSelectsInRow(rowIndex);
        return;
    }

    try {
        // التحقق من البيانات المحفوظة مسبقاً
        if (locationPersonnelMap[locationId]) {
            console.log('✅ استخدام البيانات المحفوظة مسبقاً');
            updatePersonnelSelectsInRow(rowIndex, locationPersonnelMap[locationId]);
            return;
        }

        console.log(`📡 محاولة جلب أفراد الموقع من الخادم...`);

        const response = await fetch(`/duties/api/get-location-personnel/${locationId}`);
        console.log(`📡 استجابة الخادم - Status: ${response.status}`);

        if (response.ok) {
            const data = await response.json();
            console.log('📄 بيانات الاستجابة:', data);

            if (data.success) {
                if (data.personnel && data.personnel.length > 0) {
                    console.log(`✅ تم جلب ${data.personnel.length} فرد من قاعدة البيانات`);
                    locationPersonnelMap[locationId] = data.personnel;
                    updatePersonnelSelectsInRow(rowIndex, data.personnel);
                } else {
                    console.log('⚠️ لا يوجد أفراد مرتبطين بهذا الموقع في قاعدة البيانات');
                    clearPersonnelSelectsInRow(rowIndex);
                }
            } else {
                console.log('❌ فشل في جلب الأفراد:', data.message || data.error);
                clearPersonnelSelectsInRow(rowIndex);
            }
        } else {
            console.log(`❌ خطأ في الاستجابة: ${response.status} - ${response.statusText}`);
            clearPersonnelSelectsInRow(rowIndex);
        }

    } catch (error) {
        console.error('❌ خطأ عام في تحميل أفراد الموقع:', error);
        clearPersonnelSelectsInRow(rowIndex);
    }
}

// دالة لمسح قوائم الأفراد في صف معين
function clearPersonnelSelectsInRow(rowIndex) {
    console.log(`🧹 مسح قوائم الأفراد في الصف ${rowIndex}`);

    const row = document.querySelector(`#dutyTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) {
        console.error(`❌ لم يتم العثور على الصف ${rowIndex + 1}`);
        return;
    }

    const personnelSelects = row.querySelectorAll('.personnel-select');
    personnelSelects.forEach(select => {
        select.innerHTML = '<option value="">اختر الفرد</option>';
        select.disabled = true;
    });
}

// تحديث قوائم الأفراد في صف
function updatePersonnelSelectsInRow(rowIndex, personnel) {
    console.log(`🔄 تحديث قوائم الأفراد للصف ${rowIndex}`);

    const row = document.querySelector(`#dutyTable tbody tr:nth-child(${rowIndex + 1})`);
    console.log('📍 الصف الموجود:', row);

    if (!row) {
        console.error(`❌ لم يتم العثور على الصف ${rowIndex + 1}`);
        return;
    }

    const personnelSelects = row.querySelectorAll('.personnel-select');
    console.log(`👥 عدد قوائم الأفراد الموجودة: ${personnelSelects.length}`);

    // حفظ الاختيارات الحالية قبل التحديث
    const currentSelections = [];
    personnelSelects.forEach(select => {
        currentSelections.push(select.value);
    });
    console.log(`💾 الاختيارات الحالية: [${currentSelections.join(', ')}]`);

    personnelSelects.forEach((select, index) => {
        console.log(`🔄 تحديث القائمة ${index + 1}`);
        const currentValue = currentSelections[index] || select.value;
        select.disabled = false;
        select.innerHTML = '<option value="">اختر الفرد</option>';

        // الحصول على الأفراد المختارين في نفس الصف (باستثناء القائمة الحالية)
        const selectedPersonnelInRow = getSelectedPersonnelInRow(row, select);
        console.log(`🚫 الأفراد المختارين في الصف: [${selectedPersonnelInRow.join(', ')}]`);

        personnel.forEach(person => {
            // إخفاء الفرد إذا كان مختاراً في قائمة أخرى في نفس الصف
            // لكن اسمح بإظهاره إذا كان مختاراً في القائمة الحالية
            if (!selectedPersonnelInRow.includes(person.id.toString()) || person.id.toString() === currentValue) {
                const option = document.createElement('option');
                option.value = person.id;
                // استخدام display_name إذا كان متوفراً، وإلا استخدام التنسيق العادي
                option.textContent = person.display_name || `${person.name} (${person.rank})`;
                if (person.id == currentValue) {
                    option.selected = true;
                    console.log(`✅ تم استعادة اختيار الفرد: ${person.name}`);
                }
                select.appendChild(option);
            }
        });

        // التأكد من تطبيق الاختيار
        if (currentValue && select.value !== currentValue) {
            select.value = currentValue;
        }

        console.log(`✅ تم تحديث القائمة ${index + 1} بـ ${select.options.length - 1} فرد متاح`);
    });
}

// دالة للحصول على الأفراد المختارين في نفس الصف
function getSelectedPersonnelInRow(row, excludeSelect = null) {
    const selectedIds = [];
    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        // تجاهل القائمة المحددة (القائمة التي يتم تحديثها حالياً)
        if (select !== excludeSelect && select.value && select.value !== '') {
            selectedIds.push(select.value);
        }
    });

    return selectedIds;
}

// دالة لتحديث قوائم الأفراد في صف بعد تغيير الاختيار
function refreshPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#dutyTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    // الحصول على الموقع المختار في هذا الصف
    const locationSelect = row.querySelector('select:first-child');
    if (!locationSelect || !locationSelect.value) return;

    const locationId = locationSelect.value;

    // الحصول على أفراد الموقع من الذاكرة المؤقتة
    const personnel = locationPersonnelMap[locationId];
    if (!personnel) return;

    console.log(`🔄 تحديث قوائم الأفراد في الصف ${rowIndex} بعد تغيير الاختيار`);
    updatePersonnelSelectsInRow(rowIndex, personnel);
}

// تفريغ قوائم الأفراد في صف
function clearPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#dutyTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');
    
    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

// إنشاء رأس الجدول
function generateTableHeader() {
    const thead = document.getElementById('tableHeader');
    if (!thead) return;
    
    thead.innerHTML = '';
    const headerRow = document.createElement('tr');
    
    dutyData.headers.forEach((header, index) => {
        const th = document.createElement('th');
        th.className = 'text-center';

        if (index === 0) {
            // عمود الرقم - بدون أيقونات
            th.textContent = header;
        } else {
            // باقي الأعمدة مع أيقونات التحكم
            th.innerHTML = `
                <input type="text" class="editable-header" value="${header}"
                       onchange="updateHeader(${index}, this.value)"
                       onblur="updateHeader(${index}, this.value)">
                <div class="column-controls">
                    <button type="button" class="control-btn btn-add" onclick="addColumnAfter(${index})" title="إضافة عمود">
                        <i class="fas fa-plus"></i>
                    </button>
                    ${dutyData.headers.length > 3 ? `
                    <button type="button" class="control-btn btn-delete" onclick="deleteColumn(${index})" title="حذف العمود">
                        <i class="fas fa-times"></i>
                    </button>
                    ` : ''}
                </div>
            `;
        }

        headerRow.appendChild(th);
    });
    
    thead.appendChild(headerRow);
}

// إنشاء محتوى الجدول
function generateTableBody() {
    const tbody = document.getElementById('receiptTableBody');
    if (!tbody) return;
    
    // إنشاء صفوف افتراضية إذا لم تكن موجودة
    if (dutyData.rows.length === 0) {
        for (let i = 0; i < 10; i++) {
            dutyData.rows.push(Array(dutyData.headers.length).fill(''));
        }
    }
    
    tbody.innerHTML = '';
    
    dutyData.rows.forEach((rowData, rowIndex) => {
        const row = document.createElement('tr');
        
        rowData.forEach((cellData, cellIndex) => {
            const td = document.createElement('td');
            td.className = 'text-center align-middle';
            
            if (cellIndex === 0) {
                // عمود الرقم مع أيقونات التحكم
                td.className = 'text-center align-middle row-number-cell';
                td.innerHTML = `
                    <span class="row-number">${rowIndex + 1}</span>
                    <div class="row-controls">
                        <button type="button" class="control-btn btn-add" onclick="addRowAfter(${rowIndex})" title="إضافة صف">
                            <i class="fas fa-plus"></i>
                        </button>
                        ${dutyData.rows.length > 1 ? `
                        <button type="button" class="control-btn btn-delete" onclick="deleteRow(${rowIndex})" title="حذف الصف">
                            <i class="fas fa-times"></i>
                        </button>
                        ` : ''}
                    </div>
                `;
            } else if (cellIndex === 1) {
                // عمود الموقع
                td.innerHTML = `
                    <select class="form-select location-select" onchange="handleLocationChange(${rowIndex}, ${cellIndex}, this.value);">
                        <option value="">اختر الموقع</option>
                    </select>
                `;
                
                const select = td.querySelector('.location-select');
                locationsDatabase.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    if (location.id == cellData) {
                        option.selected = true;
                    }
                    select.appendChild(option);
                });
            } else if (cellIndex >= 2 && cellIndex <= 7) {
                // خانات الأفراد
                td.innerHTML = `
                    <select class="form-select personnel-select" onchange="updateCell(${rowIndex}, ${cellIndex}, this.value);" disabled>
                        <option value="">اختر موقع أولاً</option>
                    </select>
                `;
            } else {
                // خانة الملاحظات
                td.innerHTML = `
                    <input type="text" class="form-control" value="${cellData}" onchange="updateCell(${rowIndex}, ${cellIndex}, this.value)">
                `;
            }
            
            row.appendChild(td);
        });
        
        tbody.appendChild(row);
    });
}

// تحديث خلية
function updateCell(rowIndex, cellIndex, value) {
    if (dutyData.rows[rowIndex]) {
        const oldValue = dutyData.rows[rowIndex][cellIndex];
        dutyData.rows[rowIndex][cellIndex] = value;

        console.log(`📝 تحديث خلية [${rowIndex}, ${cellIndex}]: "${oldValue}" → "${value}"`);

        // إذا كان التغيير في خانة فرد، قم بتحديث القوائم لتجنب التكرار
        if (cellIndex >= 2 && cellIndex <= 7) {
            console.log(`👥 تحديث قوائم الأفراد للصف ${rowIndex} بعد تغيير الفرد`);
            refreshPersonnelSelectsInRow(rowIndex);
        }

        // حفظ البيانات تلقائياً في localStorage
        saveDataToLocalStorage();

        // حفظ في قاعدة البيانات
        console.log(`💾 حفظ فوري بعد تحديث الخلية [${rowIndex}, ${cellIndex}]`);
        saveDutyDataToServer();
    }
}

// معالجة تغيير الموقع في الجدول الرئيسي
function handleLocationChange(rowIndex, cellIndex, locationId) {
    console.log(`🔄 تم اختيار موقع: ${locationId} للصف: ${rowIndex}`);

    // تحديث البيانات
    updateCell(rowIndex, cellIndex, locationId);

    // تحميل أفراد الموقع
    if (locationId) {
        loadPersonnelForLocation(locationId, rowIndex);
    } else {
        clearPersonnelSelectsInRow(rowIndex);
    }

    // حفظ فوري عند تغيير الموقع
    console.log('💾 حفظ فوري بعد تغيير الموقع');
    saveDataToLocalStorage();
}

// معالجة تغيير الموقع في جدول الدوريات
function handlePatrolLocationChange(rowIndex, cellIndex, locationId) {
    console.log(`🔄 تم اختيار موقع دورية: ${locationId} للصف: ${rowIndex}`);

    // تحديث البيانات
    updatePatrolCell(rowIndex, cellIndex, locationId);

    // تحميل أفراد الموقع
    if (locationId) {
        loadPersonnelForPatrolLocation(locationId, rowIndex);
    } else {
        clearPatrolPersonnelSelectsInRow(rowIndex);
    }

    // حفظ فوري
    console.log('💾 حفظ فوري بعد تغيير موقع الدورية');
    saveDataToLocalStorage();
}

// معالجة تغيير الموقع في جدول المناوبات
function handleShiftsLocationChange(rowIndex, cellIndex, locationId) {
    console.log(`🔄 تم اختيار موقع مناوبة: ${locationId} للصف: ${rowIndex}`);

    // تحديث البيانات
    updateShiftsCell(rowIndex, cellIndex, locationId);

    // تحميل أفراد الموقع
    if (locationId) {
        loadPersonnelForShiftsLocation(locationId, rowIndex);
    } else {
        clearShiftsPersonnelSelectsInRow(rowIndex);
    }

    // حفظ فوري
    console.log('💾 حفظ فوري بعد تغيير موقع المناوبة');
    saveDataToLocalStorage();
}

// دوال لمسح قوائم الأفراد
function clearPatrolPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');
    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

function clearShiftsPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');
    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

// حفظ البيانات في localStorage وقاعدة البيانات
function saveDataToLocalStorage() {
    try {
        const dataToSave = {
            dutyData: dutyData,
            patrolData: patrolData,
            shiftsData: shiftsData,
            formData: {
                dayName: document.getElementById('dayName').value,
                hijriDate: document.getElementById('hijriDate').value,
                gregorianDate: document.getElementById('gregorianDate').value,
                receiptNumber: document.getElementById('receiptNumber').value
            },
            timestamp: new Date().toISOString()
        };

        // حفظ محلي فوري
        localStorage.setItem('dutyFormData', JSON.stringify(dataToSave));
        console.log('💾 تم حفظ البيانات محلياً');

        // حفظ في قاعدة البيانات (مع تأخير لتجنب الطلبات المتكررة)
        saveDraftToDatabase(dataToSave);

    } catch (error) {
        console.error('❌ خطأ في حفظ البيانات محلياً:', error);
    }
}

// متغير لتأخير الحفظ
let saveTimeout = null;

// حفظ المسودة في قاعدة البيانات
function saveDraftToDatabase(data) {
    // إلغاء الطلب السابق إذا كان موجوداً
    if (saveTimeout) {
        clearTimeout(saveTimeout);
    }

    // تأخير الحفظ لمدة ثانية واحدة فقط لتجنب الطلبات المتكررة
    saveTimeout = setTimeout(async () => {
        try {
            console.log('🌐 حفظ المسودة في قاعدة البيانات...');

            const response = await fetch('/duties/api/save-draft', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    console.log('✅ تم حفظ المسودة في قاعدة البيانات');
                    // إشعار صغير وسريع
                    showNotification('💾 تم الحفظ', 'success', 2000);
                } else {
                    console.error('❌ فشل في حفظ المسودة:', result.error);
                    showNotification('❌ فشل في الحفظ: ' + result.error, 'error');
                }
            } else {
                console.error('❌ خطأ في الاستجابة:', response.status);
                showNotification('❌ خطأ في الحفظ', 'error');
            }
        } catch (error) {
            console.error('❌ خطأ في حفظ المسودة:', error);
            showNotification('❌ خطأ في الاتصال', 'error');
        }
    }, 1000); // تأخير لمدة ثانية واحدة
}

// استرجاع البيانات من localStorage وقاعدة البيانات
async function loadDataFromLocalStorage() {
    try {
        // فحص ما إذا كانت البيانات قد تم مسحها مؤخراً
        const dataCleared = localStorage.getItem('dutyDataCleared');
        if (dataCleared) {
            const clearTime = parseInt(dataCleared);
            const now = Date.now();
            // إذا تم المسح خلال آخر 30 ثانية، لا تحمل البيانات
            if (now - clearTime < 30000) {
                console.log('⚠️ تم مسح البيانات مؤخراً - تجاهل التحميل');
                return false;
            } else {
                // إزالة العلامة بعد انتهاء المدة
                localStorage.removeItem('dutyDataCleared');
            }
        }

        // أولاً: محاولة تحميل من قاعدة البيانات
        const databaseData = await loadDraftFromDatabase();
        if (databaseData) {
            console.log('✅ تم استرجاع البيانات من قاعدة البيانات');
            return true;
        }

        // ثانياً: التحميل من localStorage كبديل
        const savedData = localStorage.getItem('dutyFormData');
        if (savedData) {
            const data = JSON.parse(savedData);
            applyLoadedData(data);
            console.log('✅ تم استرجاع البيانات المحفوظة محلياً');
            return true;
        }
    } catch (error) {
        console.error('❌ خطأ في استرجاع البيانات:', error);
    }
    return false;
}

// تحميل المسودة من قاعدة البيانات
async function loadDraftFromDatabase() {
    try {
        console.log('🌐 تحميل المسودة من قاعدة البيانات...');

        const response = await fetch('/duties/api/load-draft');
        if (response.ok) {
            const result = await response.json();
            if (result.success && result.data) {
                applyLoadedData(result.data);
                showNotification(`تم تحميل المسودة (آخر تحديث: ${result.last_updated})`, 'info');
                return true;
            } else {
                console.log('⚠️ لا توجد مسودة محفوظة في قاعدة البيانات');
                return false;
            }
        } else {
            console.error('❌ خطأ في تحميل المسودة:', response.status);
            return false;
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل المسودة من قاعدة البيانات:', error);
        return false;
    }
}

// تطبيق البيانات المحملة
function applyLoadedData(data) {
    console.log('🔄 تطبيق البيانات المحملة:', data);

    // استرجاع بيانات الجداول
    if (data.dutyData) {
        dutyData = data.dutyData;
        console.log('✅ تم استرجاع بيانات الواجبات:', dutyData.rows.length, 'صف');
        console.log('📋 بيانات الواجبات:', dutyData.rows);
    }
    if (data.patrolData) {
        patrolData = data.patrolData;
        console.log('✅ تم استرجاع بيانات الدوريات:', patrolData.rows.length, 'صف');
        console.log('📋 بيانات الدوريات:', patrolData.rows);
    }
    if (data.shiftsData) {
        shiftsData = data.shiftsData;
        console.log('✅ تم استرجاع بيانات المناوبات:', shiftsData.rows.length, 'صف');
        console.log('📋 بيانات المناوبات:', shiftsData.rows);
    }

    // استرجاع بيانات النموذج
    if (data.formData) {
        if (data.formData.dayName) document.getElementById('dayName').value = data.formData.dayName;
        if (data.formData.hijriDate) document.getElementById('hijriDate').value = data.formData.hijriDate;
        if (data.formData.gregorianDate) document.getElementById('gregorianDate').value = data.formData.gregorianDate;
        if (data.formData.receiptNumber) document.getElementById('receiptNumber').value = data.formData.receiptNumber;
        console.log('✅ تم استرجاع بيانات النموذج');
    }

    // إعادة إنشاء الجداول لتطبيق البيانات المحملة
    setTimeout(() => {
        console.log('🔄 إعادة إنشاء الجداول مع البيانات المحملة...');
        generateTable();
        generatePatrolTable();
        generateShiftsTable();

        // تحميل أفراد المواقع المختارة
        restoreLocationPersonnel();
    }, 100);
}

// دالة لاستعادة أفراد المواقع المختارة
async function restoreLocationPersonnel() {
    console.log('🔄 استعادة أفراد المواقع المختارة...');

    // انتظار قصير للتأكد من أن الجداول تم إنشاؤها
    await new Promise(resolve => setTimeout(resolve, 200));

    // استعادة أفراد الواجبات الرئيسية
    for (let rowIndex = 0; rowIndex < dutyData.rows.length; rowIndex++) {
        const row = dutyData.rows[rowIndex];
        const locationId = row[1]; // عمود الموقع

        if (locationId) {
            console.log(`🔄 استعادة الصف ${rowIndex}: موقع ${locationId}`);

            // تحميل أفراد الموقع أولاً
            await loadPersonnelForLocation(locationId, rowIndex);

            // انتظار قصير ثم تطبيق الاختيارات المحفوظة
            await new Promise(resolve => setTimeout(resolve, 100));

            // تطبيق اختيارات الأفراد المحفوظة
            await restorePersonnelSelections(rowIndex, row, 'duty');
        }
    }

    // استعادة أفراد الدوريات
    for (let rowIndex = 0; rowIndex < patrolData.rows.length; rowIndex++) {
        const row = patrolData.rows[rowIndex];
        const locationId = row[1]; // عمود الموقع

        if (locationId) {
            await loadPersonnelForPatrolLocation(locationId, rowIndex);
            await new Promise(resolve => setTimeout(resolve, 100));
            await restorePersonnelSelections(rowIndex, row, 'patrol');
        }
    }

    // استعادة أفراد المناوبات
    for (let rowIndex = 0; rowIndex < shiftsData.rows.length; rowIndex++) {
        const row = shiftsData.rows[rowIndex];
        const locationId = row[1]; // عمود الموقع

        if (locationId) {
            await loadPersonnelForShiftsLocation(locationId, rowIndex);
            await new Promise(resolve => setTimeout(resolve, 100));
            await restorePersonnelSelections(rowIndex, row, 'shifts');
        }
    }

    console.log('✅ تم استعادة جميع أفراد المواقع والاختيارات');
}

// دالة لاستعادة اختيارات الأفراد المحفوظة
async function restorePersonnelSelections(rowIndex, rowData, tableType) {
    let tableSelector, startCol, endCol;

    // تحديد نوع الجدول والأعمدة
    switch (tableType) {
        case 'duty':
            tableSelector = '#dutyTable';
            startCol = 2;
            endCol = 7;
            break;
        case 'patrol':
            tableSelector = '#patrolTable';
            startCol = 2;
            endCol = 5;
            break;
        case 'shifts':
            tableSelector = '#shiftsTable';
            startCol = 2;
            endCol = 4;
            break;
        default:
            return;
    }

    const row = document.querySelector(`${tableSelector} tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) {
        console.error(`❌ لم يتم العثور على الصف ${rowIndex + 1} في ${tableSelector}`);
        return;
    }

    console.log(`🔄 استعادة اختيارات الأفراد للصف ${rowIndex} في ${tableType}`);

    // تطبيق اختيارات الأفراد
    for (let colIndex = startCol; colIndex <= endCol; colIndex++) {
        const personnelId = rowData[colIndex];
        if (personnelId) {
            const select = row.querySelector(`td:nth-child(${colIndex + 1}) select.personnel-select`);
            if (select) {
                // البحث عن الخيار المطابق وتحديده
                const option = select.querySelector(`option[value="${personnelId}"]`);
                if (option) {
                    select.value = personnelId;
                    console.log(`✅ تم استعادة اختيار الفرد ${personnelId} في العمود ${colIndex}`);
                } else {
                    console.warn(`⚠️ لم يتم العثور على الفرد ${personnelId} في القائمة`);
                }
            }
        }
    }
}

// دوال مساعدة لتحميل أفراد المواقع للجداول الأخرى
async function loadPersonnelForPatrolLocation(locationId, rowIndex) {
    try {
        const personnel = locationPersonnelMap[locationId];
        if (personnel) {
            const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
            if (row) {
                updatePatrolPersonnelSelects(row, personnel);
            }
        } else {
            await loadPersonnelForLocation(locationId, rowIndex);
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد الدوريات:', error);
    }
}

async function loadPersonnelForShiftsLocation(locationId, rowIndex) {
    try {
        const personnel = locationPersonnelMap[locationId];
        if (personnel) {
            const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
            if (row) {
                updateShiftsPersonnelSelects(row, personnel);
            }
        } else {
            await loadPersonnelForLocation(locationId, rowIndex);
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد المناوبات:', error);
    }
}

// دالة لإظهار الإشعارات
function showNotification(message, type = 'info', duration = 5000) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 250px;
        max-width: 400px;
        font-size: 14px;
    `;

    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, duration);
}

// إنشاء الجدول الرئيسي
function generateTable() {
    // إضافة class receipts-table للجدول الرئيسي لتوحيد التنسيق
    const dutyTable = document.getElementById('dutyTable');
    if (dutyTable) {
        dutyTable.classList.add('receipts-table');
        console.log('✅ تم إضافة class receipts-table للجدول الرئيسي');
    }

    generateTableHeader();
    generateTableBody();
}

// إنشاء جدول الدوريات
function generatePatrolTable() {
    // إضافة class receipts-table لجدول الدوريات لتطبيق نفس تنسيقات كشف الاستلامات
    const patrolTable = document.getElementById('patrolTable');
    if (patrolTable) {
        patrolTable.classList.add('receipts-table');
        console.log('✅ تم إضافة class receipts-table لجدول الدوريات');
        console.log('📋 Classes الحالية للجدول:', patrolTable.className);

        // التأكد من إضافة class بقوة
        if (!patrolTable.classList.contains('receipts-table')) {
            patrolTable.className += ' receipts-table';
            console.log('🔧 تم إضافة class بقوة');
        }
    } else {
        console.error('❌ لم يتم العثور على جدول الدوريات');
    }

    generatePatrolTableHeader();
    generatePatrolTableBody();

    // التحقق مرة أخرى بعد إنشاء الجدول
    setTimeout(() => {
        const table = document.getElementById('patrolTable');
        if (table) {
            console.log('🔍 فحص نهائي - Classes:', table.className);
            console.log('🔍 هل يحتوي على receipts-table؟', table.classList.contains('receipts-table'));
        }
    }, 100);
}

// إنشاء جدول المناوبين
function generateShiftsTable() {
    // إضافة class receipts-table لجدول المناوبين لتوحيد التنسيق
    const shiftsTable = document.getElementById('shiftsTable');
    if (shiftsTable) {
        shiftsTable.classList.add('receipts-table');
        console.log('✅ تم إضافة class receipts-table لجدول المناوبين');
    }

    generateShiftsTableHeader();
    generateShiftsTableBody();
}

// إنشاء رأس جدول الدوريات
function generatePatrolTableHeader() {
    const thead = document.getElementById('patrolTableHeader');
    if (!thead) return;

    thead.innerHTML = '';
    const headerRow = document.createElement('tr');

    patrolData.headers.forEach((header, index) => {
        const th = document.createElement('th');
        th.className = 'text-center';

        if (index === 0) {
            th.textContent = header;
        } else {
            th.innerHTML = `
                <input type="text" class="editable-header" value="${header}"
                       onchange="updatePatrolHeader(${index}, this.value)"
                       onblur="updatePatrolHeader(${index}, this.value)">
                <div class="column-controls">
                    <button type="button" class="control-btn btn-add" onclick="addPatrolColumnAfter(${index})" title="إضافة عمود">
                        <i class="fas fa-plus"></i>
                    </button>
                    ${patrolData.headers.length > 3 ? `
                    <button type="button" class="control-btn btn-delete" onclick="deletePatrolColumn(${index})" title="حذف العمود">
                        <i class="fas fa-times"></i>
                    </button>
                    ` : ''}
                </div>
            `;
        }

        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
}

// إنشاء محتوى جدول الدوريات
function generatePatrolTableBody() {
    const tbody = document.getElementById('patrolTableBody');
    if (!tbody) return;

    // إنشاء صفوف افتراضية إذا لم تكن موجودة
    if (patrolData.rows.length === 0) {
        for (let i = 0; i < 6; i++) {
            patrolData.rows.push(Array(patrolData.headers.length).fill(''));
        }
    }

    tbody.innerHTML = '';

    patrolData.rows.forEach((rowData, rowIndex) => {
        const row = document.createElement('tr');

        rowData.forEach((cellData, cellIndex) => {
            const td = document.createElement('td');
            td.className = 'text-center align-middle';

            if (cellIndex === 0) {
                // عمود الرقم مع أيقونات التحكم
                td.className = 'text-center align-middle row-number-cell';
                td.innerHTML = `
                    <span class="row-number">${rowIndex + 1}</span>
                    <div class="row-controls">
                        <button type="button" class="control-btn btn-add" onclick="addPatrolRowAfter(${rowIndex})" title="إضافة صف">
                            <i class="fas fa-plus"></i>
                        </button>
                        ${patrolData.rows.length > 1 ? `
                        <button type="button" class="control-btn btn-delete" onclick="deletePatrolRow(${rowIndex})" title="حذف الصف">
                            <i class="fas fa-times"></i>
                        </button>
                        ` : ''}
                    </div>
                `;
            } else if (cellIndex === 1) {
                // عمود الموقع
                td.innerHTML = `
                    <select class="form-select location-select" onchange="handlePatrolLocationChange(${rowIndex}, ${cellIndex}, this.value);">
                        <option value="">اختر الموقع</option>
                    </select>
                `;

                const select = td.querySelector('.location-select');
                locationsDatabase.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    if (location.id == cellData) {
                        option.selected = true;
                    }
                    select.appendChild(option);
                });
            } else if (cellIndex >= 2 && cellIndex < patrolData.headers.length - 1) {
                // خانات الأفراد (كل الأعمدة ما عدا الأخير الذي هو الملاحظات)
                td.innerHTML = `
                    <select class="form-select personnel-select" onchange="updatePatrolCell(${rowIndex}, ${cellIndex}, this.value);" disabled>
                        <option value="">اختر موقع أولاً</option>
                    </select>
                `;
            } else {
                // خانة الملاحظات (العمود الأخير)
                td.innerHTML = `
                    <input type="text" class="form-control" value="${cellData}" onchange="updatePatrolCell(${rowIndex}, ${cellIndex}, this.value)">
                `;
            }

            row.appendChild(td);
        });

        tbody.appendChild(row);
    });
}

// إنشاء رأس جدول المناوبين
function generateShiftsTableHeader() {
    const thead = document.getElementById('shiftsTableHeader');
    if (!thead) return;

    thead.innerHTML = '';
    const headerRow = document.createElement('tr');

    shiftsData.headers.forEach((header, index) => {
        const th = document.createElement('th');
        th.className = 'text-center';

        if (index === 0) {
            th.textContent = header;
        } else {
            th.innerHTML = `
                <input type="text" class="editable-header" value="${header}"
                       onchange="updateShiftsHeader(${index}, this.value)"
                       onblur="updateShiftsHeader(${index}, this.value)">
                <div class="column-controls">
                    <button type="button" class="control-btn btn-add" onclick="addShiftsColumnAfter(${index})" title="إضافة عمود">
                        <i class="fas fa-plus"></i>
                    </button>
                    ${shiftsData.headers.length > 3 ? `
                    <button type="button" class="control-btn btn-delete" onclick="deleteShiftsColumn(${index})" title="حذف العمود">
                        <i class="fas fa-times"></i>
                    </button>
                    ` : ''}
                </div>
            `;
        }

        headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
}

// إنشاء محتوى جدول المناوبين
function generateShiftsTableBody() {
    const tbody = document.getElementById('shiftsTableBody');
    if (!tbody) return;

    // إنشاء صفوف افتراضية إذا لم تكن موجودة
    if (shiftsData.rows.length === 0) {
        for (let i = 0; i < 2; i++) {
            shiftsData.rows.push(Array(shiftsData.headers.length).fill(''));
        }
    }

    tbody.innerHTML = '';

    shiftsData.rows.forEach((rowData, rowIndex) => {
        const row = document.createElement('tr');

        rowData.forEach((cellData, cellIndex) => {
            const td = document.createElement('td');
            td.className = 'text-center align-middle';

            if (cellIndex === 0) {
                // عمود الرقم مع أيقونات التحكم
                td.className = 'text-center align-middle row-number-cell';
                td.innerHTML = `
                    <span class="row-number">${rowIndex + 1}</span>
                    <div class="row-controls">
                        <button type="button" class="control-btn btn-add" onclick="addShiftsRowAfter(${rowIndex})" title="إضافة صف">
                            <i class="fas fa-plus"></i>
                        </button>
                        ${shiftsData.rows.length > 1 ? `
                        <button type="button" class="control-btn btn-delete" onclick="deleteShiftsRow(${rowIndex})" title="حذف الصف">
                            <i class="fas fa-times"></i>
                        </button>
                        ` : ''}
                    </div>
                `;
            } else if (cellIndex === 1) {
                // عمود الموقع
                td.innerHTML = `
                    <select class="form-select location-select" onchange="handleShiftsLocationChange(${rowIndex}, ${cellIndex}, this.value);">
                        <option value="">اختر الموقع</option>
                    </select>
                `;

                const select = td.querySelector('.location-select');
                locationsDatabase.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    if (location.id == cellData) {
                        option.selected = true;
                    }
                    select.appendChild(option);
                });
            } else if (cellIndex >= 2 && cellIndex <= 4) {
                // خانات الأفراد
                td.innerHTML = `
                    <select class="form-select personnel-select" onchange="updateShiftsCell(${rowIndex}, ${cellIndex}, this.value);" disabled>
                        <option value="">اختر موقع أولاً</option>
                    </select>
                `;
            } else {
                // خانة الملاحظات
                td.innerHTML = `
                    <input type="text" class="form-control" value="${cellData}" onchange="updateShiftsCell(${rowIndex}, ${cellIndex}, this.value)">
                `;
            }

            row.appendChild(td);
        });

        tbody.appendChild(row);
    });
}

// تحديث خلايا الجداول الأخرى
function updatePatrolCell(rowIndex, cellIndex, value) {
    if (patrolData.rows[rowIndex]) {
        patrolData.rows[rowIndex][cellIndex] = value;

        // إذا كان التغيير في خانة فرد، قم بتحديث القوائم لتجنب التكرار
        if (cellIndex >= 2 && cellIndex <= 5) {
            refreshPatrolPersonnelSelectsInRow(rowIndex);
        }

        saveDataToLocalStorage();

        // حفظ في قاعدة البيانات
        savePatrolDataToServer();
    }
}

// تحميل أفراد الموقع - جدول الدوريات
async function loadPersonnelForPatrolLocation(locationId, rowIndex) {
    console.log(`🔄 تحميل أفراد الموقع ${locationId} للدوريات - الصف ${rowIndex}`);

    if (!locationId) {
        console.log('⚠️ لا يوجد معرف موقع');
        clearPatrolPersonnelSelectsInRow(rowIndex);
        return;
    }

    try {
        if (locationPersonnelMap[locationId]) {
            console.log('✅ استخدام البيانات المحفوظة مسبقاً');
            updatePatrolPersonnelSelectsInRow(rowIndex, locationPersonnelMap[locationId]);
            return;
        }

        console.log(`📡 جلب أفراد الموقع من الخادم: /duties/api/get-location-personnel/${locationId}`);
        const response = await fetch(`/duties/api/get-location-personnel/${locationId}`);

        if (response.ok) {
            const data = await response.json();
            console.log('📄 استجابة الخادم:', data);

            if (data.success) {
                console.log(`✅ تم جلب ${data.personnel.length} فرد من الخادم`);
                locationPersonnelMap[locationId] = data.personnel;
                updatePatrolPersonnelSelectsInRow(rowIndex, data.personnel);
            } else {
                console.log('❌ فشل في جلب الأفراد:', data.error);
                clearPatrolPersonnelSelectsInRow(rowIndex);
            }
        } else {
            console.log('❌ خطأ في الاستجابة:', response.status);
            clearPatrolPersonnelSelectsInRow(rowIndex);
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد الموقع:', error);
        clearPatrolPersonnelSelectsInRow(rowIndex);
    }
}

// تحديث قوائم الأفراد في صف - جدول الدوريات
function updatePatrolPersonnelSelectsInRow(rowIndex, personnel) {
    console.log(`🔄 تحديث قوائم الأفراد للدوريات - الصف ${rowIndex}`);

    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    console.log('📍 الصف الموجود:', row);

    if (!row) {
        console.error(`❌ لم يتم العثور على الصف ${rowIndex + 1}`);
        return;
    }

    const personnelSelects = row.querySelectorAll('.personnel-select');
    console.log(`👥 عدد قوائم الأفراد الموجودة: ${personnelSelects.length}`);

    personnelSelects.forEach((select, index) => {
        console.log(`🔄 تحديث القائمة ${index + 1}`);
        const currentValue = select.value;
        select.disabled = false;
        select.innerHTML = '<option value="">اختر الفرد</option>';

        personnel.forEach(person => {
            const option = document.createElement('option');
            option.value = person.id;
            option.textContent = `${person.name} (${person.rank})`;
            if (person.id == currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });

        console.log(`✅ تم تحديث القائمة ${index + 1} بـ ${personnel.length} فرد`);
    });
}

// تفريغ قوائم الأفراد في صف - جدول الدوريات
function clearPatrolPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

function updateShiftsCell(rowIndex, cellIndex, value) {
    if (shiftsData.rows[rowIndex]) {
        shiftsData.rows[rowIndex][cellIndex] = value;

        // إذا كان التغيير في خانة فرد، قم بتحديث القوائم لتجنب التكرار
        if (cellIndex >= 2 && cellIndex <= 4) {
            refreshShiftsPersonnelSelectsInRow(rowIndex);
        }

        saveDataToLocalStorage();

        // حفظ في قاعدة البيانات
        saveShiftsDataToServer();
    }
}

// دوال مساعدة لتحديث قوائم الأفراد في الجداول الأخرى
function refreshPatrolPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const locationSelect = row.querySelector('select:first-child');
    if (!locationSelect || !locationSelect.value) return;

    const locationId = locationSelect.value;
    const personnel = locationPersonnelMap[locationId];
    if (!personnel) return;

    console.log(`🔄 تحديث قوائم أفراد الدوريات في الصف ${rowIndex}`);
    updatePatrolPersonnelSelects(row, personnel);
}

function refreshShiftsPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const locationSelect = row.querySelector('select:first-child');
    if (!locationSelect || !locationSelect.value) return;

    const locationId = locationSelect.value;
    const personnel = locationPersonnelMap[locationId];
    if (!personnel) return;

    console.log(`🔄 تحديث قوائم أفراد المناوبات في الصف ${rowIndex}`);
    updateShiftsPersonnelSelects(row, personnel);
}

// دوال لتحديث قوائم الأفراد مع تجنب التكرار
function updatePatrolPersonnelSelects(row, personnel) {
    const personnelSelects = row.querySelectorAll('.personnel-select');

    // حفظ الاختيارات الحالية
    const currentSelections = [];
    personnelSelects.forEach(select => {
        currentSelections.push(select.value);
    });

    personnelSelects.forEach((select, index) => {
        const currentValue = currentSelections[index] || select.value;
        const selectedPersonnelInRow = getSelectedPersonnelInRow(row, select);

        select.innerHTML = '<option value="">اختر الفرد</option>';

        personnel.forEach(person => {
            // السماح بإظهار الفرد إذا كان مختاراً في القائمة الحالية
            if (!selectedPersonnelInRow.includes(person.id.toString()) || person.id.toString() === currentValue) {
                const option = document.createElement('option');
                option.value = person.id;
                option.textContent = person.display_name || `${person.name} (${person.rank})`;
                if (person.id == currentValue) {
                    option.selected = true;
                }
                select.appendChild(option);
            }
        });

        // التأكد من تطبيق الاختيار
        if (currentValue && select.value !== currentValue) {
            select.value = currentValue;
        }
    });
}

function updateShiftsPersonnelSelects(row, personnel) {
    const personnelSelects = row.querySelectorAll('.personnel-select');

    // حفظ الاختيارات الحالية
    const currentSelections = [];
    personnelSelects.forEach(select => {
        currentSelections.push(select.value);
    });

    personnelSelects.forEach((select, index) => {
        const currentValue = currentSelections[index] || select.value;
        const selectedPersonnelInRow = getSelectedPersonnelInRow(row, select);

        select.innerHTML = '<option value="">اختر الفرد</option>';

        personnel.forEach(person => {
            // السماح بإظهار الفرد إذا كان مختاراً في القائمة الحالية
            if (!selectedPersonnelInRow.includes(person.id.toString()) || person.id.toString() === currentValue) {
                const option = document.createElement('option');
                option.value = person.id;
                option.textContent = person.display_name || `${person.name} (${person.rank})`;
                if (person.id == currentValue) {
                    option.selected = true;
                }
                select.appendChild(option);
            }
        });

        // التأكد من تطبيق الاختيار
        if (currentValue && select.value !== currentValue) {
            select.value = currentValue;
        }
    });
}

// تحميل أفراد الموقع - جدول المناوبين
async function loadPersonnelForShiftsLocation(locationId, rowIndex) {
    console.log(`🔄 تحميل أفراد الموقع ${locationId} للمناوبين - الصف ${rowIndex}`);

    if (!locationId) {
        console.log('⚠️ لا يوجد معرف موقع');
        clearShiftsPersonnelSelectsInRow(rowIndex);
        return;
    }

    try {
        if (locationPersonnelMap[locationId]) {
            console.log('✅ استخدام البيانات المحفوظة مسبقاً');
            updateShiftsPersonnelSelectsInRow(rowIndex, locationPersonnelMap[locationId]);
            return;
        }

        console.log(`📡 جلب أفراد الموقع من الخادم: /duties/api/get-location-personnel/${locationId}`);
        const response = await fetch(`/duties/api/get-location-personnel/${locationId}`);

        if (response.ok) {
            const data = await response.json();
            console.log('📄 استجابة الخادم:', data);

            if (data.success) {
                console.log(`✅ تم جلب ${data.personnel.length} فرد من الخادم`);
                locationPersonnelMap[locationId] = data.personnel;
                updateShiftsPersonnelSelectsInRow(rowIndex, data.personnel);
            } else {
                console.log('❌ فشل في جلب الأفراد:', data.error);
                clearShiftsPersonnelSelectsInRow(rowIndex);
            }
        } else {
            console.log('❌ خطأ في الاستجابة:', response.status);
            clearShiftsPersonnelSelectsInRow(rowIndex);
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد الموقع:', error);
        clearShiftsPersonnelSelectsInRow(rowIndex);
    }
}

// تحديث قوائم الأفراد في صف - جدول المناوبين
function updateShiftsPersonnelSelectsInRow(rowIndex, personnel) {
    console.log(`🔄 تحديث قوائم الأفراد للمناوبين - الصف ${rowIndex}`);

    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    console.log('📍 الصف الموجود:', row);

    if (!row) {
        console.error(`❌ لم يتم العثور على الصف ${rowIndex + 1}`);
        return;
    }

    const personnelSelects = row.querySelectorAll('.personnel-select');
    console.log(`👥 عدد قوائم الأفراد الموجودة: ${personnelSelects.length}`);

    personnelSelects.forEach((select, index) => {
        console.log(`🔄 تحديث القائمة ${index + 1}`);
        const currentValue = select.value;
        select.disabled = false;
        select.innerHTML = '<option value="">اختر الفرد</option>';

        personnel.forEach(person => {
            const option = document.createElement('option');
            option.value = person.id;
            option.textContent = `${person.name} (${person.rank})`;
            if (person.id == currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });

        console.log(`✅ تم تحديث القائمة ${index + 1} بـ ${personnel.length} فرد`);
    });
}

// تفريغ قوائم الأفراد في صف - جدول المناوبين
function clearShiftsPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

// تحميل أفراد الموقع للجداول الأخرى
async function loadPersonnelForPatrolLocation(locationId, rowIndex) {
    if (!locationId) {
        clearPatrolPersonnelSelectsInRow(rowIndex);
        return;
    }

    try {
        if (locationPersonnelMap[locationId]) {
            updatePatrolPersonnelSelectsInRow(rowIndex, locationPersonnelMap[locationId]);
            return;
        }

        const response = await fetch(`/duties/api/get-location-personnel/${locationId}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                locationPersonnelMap[locationId] = data.personnel;
                updatePatrolPersonnelSelectsInRow(rowIndex, data.personnel);
            } else {
                clearPatrolPersonnelSelectsInRow(rowIndex);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد الموقع:', error);
        clearPatrolPersonnelSelectsInRow(rowIndex);
    }
}

async function loadPersonnelForShiftsLocation(locationId, rowIndex) {
    if (!locationId) {
        clearShiftsPersonnelSelectsInRow(rowIndex);
        return;
    }

    try {
        if (locationPersonnelMap[locationId]) {
            updateShiftsPersonnelSelectsInRow(rowIndex, locationPersonnelMap[locationId]);
            return;
        }

        const response = await fetch(`/duties/api/get-location-personnel/${locationId}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                locationPersonnelMap[locationId] = data.personnel;
                updateShiftsPersonnelSelectsInRow(rowIndex, data.personnel);
            } else {
                clearShiftsPersonnelSelectsInRow(rowIndex);
            }
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل أفراد الموقع:', error);
        clearShiftsPersonnelSelectsInRow(rowIndex);
    }
}

// تحديث قوائم الأفراد للجداول الأخرى
function updatePatrolPersonnelSelectsInRow(rowIndex, personnel) {
    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        const currentValue = select.value;
        select.disabled = false;
        select.innerHTML = '<option value="">اختر الفرد</option>';

        personnel.forEach(person => {
            const option = document.createElement('option');
            option.value = person.id;
            option.textContent = `${person.name} (${person.rank})`;
            if (person.id == currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    });
}

function updateShiftsPersonnelSelectsInRow(rowIndex, personnel) {
    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        const currentValue = select.value;
        select.disabled = false;
        select.innerHTML = '<option value="">اختر الفرد</option>';

        personnel.forEach(person => {
            const option = document.createElement('option');
            option.value = person.id;
            option.textContent = `${person.name} (${person.rank})`;
            if (person.id == currentValue) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    });
}

// تفريغ قوائم الأفراد للجداول الأخرى
function clearPatrolPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#patrolTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

function clearShiftsPersonnelSelectsInRow(rowIndex) {
    const row = document.querySelector(`#shiftsTable tbody tr:nth-child(${rowIndex + 1})`);
    if (!row) return;

    const personnelSelects = row.querySelectorAll('.personnel-select');

    personnelSelects.forEach(select => {
        select.disabled = true;
        select.innerHTML = '<option value="">اختر موقع أولاً</option>';
        select.value = '';
    });
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', async function() {
    console.log('🚀 بدء تهيئة صفحة كشف الواجبات...');

    // تحميل المواقع أولاً
    await loadLocations();

    // محاولة تحميل البيانات من قاعدة البيانات أولاً
    console.log('🔄 محاولة تحميل البيانات من قاعدة البيانات...');

    // تحميل بيانات الجدول الرئيسي (يحتوي على جميع الجداول)
    const hasMainData = await loadDutyDataFromServer();

    // تحميل بيانات الجداول الفردية إذا لم توجد في الجدول الرئيسي
    if (!hasMainData) {
        console.log('🔄 تحميل بيانات الجداول الفردية...');
        await loadPatrolDataFromServer();
        await loadShiftsDataFromServer();
    }

    // إذا لم توجد أي بيانات في قاعدة البيانات، حمل من localStorage
    if (!hasMainData) {
        console.log('🔄 تحميل البيانات من localStorage...');
        const hasLocalData = await loadDataFromLocalStorage();
    }

    // إنشاء جميع الجداول (في جميع الحالات للتأكد)
    generateTable();
    generatePatrolTable();
    generateShiftsTable();

    // تهيئة التواريخ ورقم الكشف تلقائياً دائماً (مثل كشف الاستلامات)
    await initializeDutyReceipt();

    // حفظ أولي للبيانات في قاعدة البيانات بعد التهيئة
    setTimeout(() => {
        console.log('💾 حفظ أولي للبيانات في قاعدة البيانات...');

        // التحقق من وجود بيانات قبل الحفظ
        if (dutyData.rows.length > 0 || patrolData.rows.length > 0 || shiftsData.rows.length > 0) {
            saveDutyDataToServer();
        } else {
            console.log('ℹ️ لا توجد بيانات للحفظ حالياً');
        }
    }, 1000);

    // إضافة مستمعي الأحداث للتواريخ
    setupDateEventListeners();

    // تحديث التواريخ تلقائياً كل دقيقة
    setInterval(updateDatesAutomatically, 60000);

    // تحديث التاريخ الهجري كل ساعة للتأكد من الدقة
    setInterval(async () => {
        const hijriElement = document.getElementById('hijriDate');
        if (hijriElement) {
            const currentHijri = await getCurrentHijriDate();
            hijriElement.value = currentHijri;
            console.log('🕐 تحديث تلقائي للتاريخ الهجري:', currentHijri);
        }
    }, 3600000); // كل ساعة

    // تحديث التاريخ الهجري عند منتصف الليل (تغيير اليوم)
    function scheduleNextMidnightUpdate() {
        const now = new Date();
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);

        const msUntilMidnight = tomorrow.getTime() - now.getTime();

        setTimeout(async () => {
            console.log('🌙 منتصف الليل - تحديث التواريخ...');
            await updateDatesAutomatically();
            scheduleNextMidnightUpdate(); // جدولة التحديث التالي
        }, msUntilMidnight);
    }

    scheduleNextMidnightUpdate();

    console.log('✅ تم تهيئة صفحة كشف الواجبات بنجاح');

    // حفظ البيانات عند إغلاق الصفحة
    window.addEventListener('beforeunload', function() {
        console.log('💾 حفظ أخير قبل إغلاق الصفحة');
        saveDutyDataToServer();
        saveDataToLocalStorage();
    });
});

// تم استبدال setCurrentDate بـ initializeDutyReceipt

// دوال إضافة الصفوف والأعمدة

// إضافة صف جديد للجدول الرئيسي
function addRow() {
    dutyData.rows.push(Array(dutyData.headers.length).fill(''));
    generateTableBody();
    saveDutyDataToServer(); // حفظ في قاعدة البيانات
}

// إضافة صف بعد صف محدد
function addRowAfter(rowIndex) {
    const newRow = Array(dutyData.headers.length).fill('');
    dutyData.rows.splice(rowIndex + 1, 0, newRow);
    generateTable();
    saveDutyDataToServer(); // حفظ في قاعدة البيانات
}

// حذف صف
function deleteRow(rowIndex) {
    if (dutyData.rows.length > 1) {
        if (confirm('هل أنت متأكد من حذف هذا الصف؟')) {
            dutyData.rows.splice(rowIndex, 1);
            generateTable();
            saveDutyDataToServer(); // حفظ في قاعدة البيانات
        }
    }
}

// إضافة عمود بعد عمود محدد
function addColumnAfter(columnIndex) {
    dutyData.headers.splice(columnIndex + 1, 0, 'عمود جديد');
    dutyData.rows.forEach(row => {
        row.splice(columnIndex + 1, 0, '');
    });
    generateTable();
    saveDutyDataToServer(); // حفظ في قاعدة البيانات
}

// حذف عمود
function deleteColumn(columnIndex) {
    if (dutyData.headers.length > 3) {
        if (confirm('هل أنت متأكد من حذف هذا العمود؟')) {
            dutyData.headers.splice(columnIndex, 1);
            dutyData.rows.forEach(row => {
                row.splice(columnIndex, 1);
            });
            generateTable();
            saveDutyDataToServer(); // حفظ في قاعدة البيانات
        }
    }
}

// تحديث رأس العمود
function updateHeader(columnIndex, newValue) {
    if (dutyData.headers[columnIndex]) {
        dutyData.headers[columnIndex] = newValue;
        saveDutyDataToServer(); // حفظ في قاعدة البيانات
    }
}

// إضافة عمود جديد للجدول الرئيسي
function addColumn() {
    const newColumnName = prompt('أدخل اسم العمود الجديد:');
    if (newColumnName && newColumnName.trim()) {
        // إضافة العمود للرأس
        dutyData.headers.splice(-1, 0, newColumnName.trim()); // إدراج قبل عمود الملاحظات

        // إضافة خلية فارغة لكل صف
        dutyData.rows.forEach(row => {
            row.splice(-1, 0, ''); // إدراج قبل خلية الملاحظات
        });

        generateTable();
        saveDutyDataToServer(); // حفظ في قاعدة البيانات
    }
}

// إضافة صف جديد لجدول الدوريات
function addPatrolRow() {
    patrolData.rows.push(Array(patrolData.headers.length).fill(''));
    generatePatrolTableBody();
    savePatrolDataToServer(); // حفظ في قاعدة البيانات
}

// إضافة صف بعد صف محدد - جدول الدوريات
function addPatrolRowAfter(rowIndex) {
    const newRow = Array(patrolData.headers.length).fill('');
    patrolData.rows.splice(rowIndex + 1, 0, newRow);
    generatePatrolTable();
    savePatrolDataToServer(); // حفظ في قاعدة البيانات
}

// حذف صف - جدول الدوريات
function deletePatrolRow(rowIndex) {
    if (patrolData.rows.length > 1) {
        if (confirm('هل أنت متأكد من حذف هذا الصف؟')) {
            patrolData.rows.splice(rowIndex, 1);
            generatePatrolTable();
            savePatrolDataToServer(); // حفظ في قاعدة البيانات
        }
    }
}

// إضافة عمود بعد عمود محدد - جدول الدوريات
function addPatrolColumnAfter(columnIndex) {
    patrolData.headers.splice(columnIndex + 1, 0, 'عمود جديد');
    patrolData.rows.forEach(row => {
        row.splice(columnIndex + 1, 0, '');
    });
    generatePatrolTable();
    savePatrolDataToServer(); // حفظ في قاعدة البيانات
}

// حذف عمود - جدول الدوريات
function deletePatrolColumn(columnIndex) {
    if (patrolData.headers.length > 3) {
        if (confirm('هل أنت متأكد من حذف هذا العمود؟')) {
            patrolData.headers.splice(columnIndex, 1);
            patrolData.rows.forEach(row => {
                row.splice(columnIndex, 1);
            });
            generatePatrolTable();
            savePatrolDataToServer(); // حفظ في قاعدة البيانات
        }
    }
}

// تحديث رأس العمود - جدول الدوريات
function updatePatrolHeader(columnIndex, newValue) {
    if (patrolData.headers[columnIndex]) {
        patrolData.headers[columnIndex] = newValue;
        savePatrolDataToServer(); // حفظ في قاعدة البيانات
    }
}

// إضافة عمود جديد لجدول الدوريات
function addPatrolColumn() {
    const newColumnName = prompt('أدخل اسم العمود الجديد:');
    if (newColumnName && newColumnName.trim()) {
        patrolData.headers.splice(-1, 0, newColumnName.trim());
        patrolData.rows.forEach(row => {
            row.splice(-1, 0, '');
        });
        generatePatrolTable();
    }
}

// إضافة صف جديد لجدول المناوبين
function addShiftsRow() {
    shiftsData.rows.push(Array(shiftsData.headers.length).fill(''));
    generateShiftsTableBody();
    saveShiftsDataToServer(); // حفظ في قاعدة البيانات
}

// إضافة صف بعد صف محدد - جدول المناوبين
function addShiftsRowAfter(rowIndex) {
    const newRow = Array(shiftsData.headers.length).fill('');
    shiftsData.rows.splice(rowIndex + 1, 0, newRow);
    generateShiftsTable();
    saveShiftsDataToServer(); // حفظ في قاعدة البيانات
}

// حذف صف - جدول المناوبين
function deleteShiftsRow(rowIndex) {
    if (shiftsData.rows.length > 1) {
        if (confirm('هل أنت متأكد من حذف هذا الصف؟')) {
            shiftsData.rows.splice(rowIndex, 1);
            generateShiftsTable();
            saveShiftsDataToServer(); // حفظ في قاعدة البيانات
        }
    }
}

// إضافة عمود بعد عمود محدد - جدول المناوبين
function addShiftsColumnAfter(columnIndex) {
    shiftsData.headers.splice(columnIndex + 1, 0, 'عمود جديد');
    shiftsData.rows.forEach(row => {
        row.splice(columnIndex + 1, 0, '');
    });
    generateShiftsTable();
    saveShiftsDataToServer(); // حفظ في قاعدة البيانات
}

// حذف عمود - جدول المناوبين
function deleteShiftsColumn(columnIndex) {
    if (shiftsData.headers.length > 3) {
        if (confirm('هل أنت متأكد من حذف هذا العمود؟')) {
            shiftsData.headers.splice(columnIndex, 1);
            shiftsData.rows.forEach(row => {
                row.splice(columnIndex, 1);
            });
            generateShiftsTable();
            saveShiftsDataToServer(); // حفظ في قاعدة البيانات
        }
    }
}

// تحديث رأس العمود - جدول المناوبين
function updateShiftsHeader(columnIndex, newValue) {
    if (shiftsData.headers[columnIndex]) {
        shiftsData.headers[columnIndex] = newValue;
        saveShiftsDataToServer(); // حفظ في قاعدة البيانات
    }
}

// دالة حفظ الكشف
async function saveReceipt() {
    try {
        console.log('🔄 بدء عملية حفظ الكشف...');

        const receiptData = {
            day_name: document.getElementById('dayName').value,
            hijri_date: document.getElementById('hijriDate').value,
            gregorian_date: document.getElementById('gregorianDate').value,
            receipt_number: document.getElementById('receiptNumber').value,
            duty_data: dutyData,
            patrol_data: patrolData,
            shifts_data: shiftsData
        };

        console.log('📋 بيانات الكشف:', receiptData);

        // إعداد headers للطلب
        let headers = {
            'Content-Type': 'application/json'
        };

        // محاولة الحصول على CSRF token (اختياري لأنه معطل)
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const tokenValue = csrfToken.getAttribute('content');
            console.log('🔐 CSRF token:', tokenValue);
            if (tokenValue && tokenValue !== 'dummy_csrf_token') {
                headers['X-CSRFToken'] = tokenValue;
            }
        }

        console.log('📡 إرسال الطلب إلى الخادم...');
        const response = await fetch('/duties/api/save-receipt', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(receiptData)
        });

        console.log('📡 استجابة الخادم:', response.status, response.statusText);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('📄 نتيجة الحفظ:', result);

        if (result.success) {
            alert('✅ تم حفظ الكشف بنجاح!');
            // حفظ محلي كنسخة احتياطية
            localStorage.setItem('lastDutyReceipt', JSON.stringify(receiptData));
        } else {
            alert('❌ خطأ في حفظ الكشف: ' + (result.message || 'خطأ غير معروف'));
        }
    } catch (error) {
        console.error('❌ خطأ في حفظ الكشف:', error);

        // حفظ محلي في حالة فشل الحفظ على الخادم
        try {
            localStorage.setItem('lastDutyReceipt', JSON.stringify(receiptData));
            alert('❌ فشل الحفظ على الخادم، ولكن تم حفظ نسخة محلية.\nخطأ: ' + error.message);
        } catch (localError) {
            alert('❌ خطأ في حفظ الكشف: ' + error.message);
        }
    }
}

// مسح جميع البيانات
async function clearAllData() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟ لن يمكن التراجع عن هذا الإجراء.')) {
        try {
            console.log('🗑️ بدء مسح جميع البيانات...');

            // مسح البيانات من localStorage
            localStorage.removeItem('dutyFormData');
            localStorage.removeItem('lastDutyReceipt');

            // تسجيل وقت المسح لمنع إعادة التحميل
            localStorage.setItem('dutyDataCleared', Date.now().toString());

            // مسح المسودة من قاعدة البيانات
            try {
                const response = await fetch('/duties/api/clear-draft', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    }
                });

                if (response.ok) {
                    console.log('✅ تم مسح المسودة من قاعدة البيانات');

                    // تأكيد إضافي: حفظ بيانات فارغة في قاعدة البيانات
                    const emptyData = {
                        dutyData: { headers: [...DEFAULT_HEADERS], rows: [] },
                        patrolData: { headers: [...DEFAULT_PATROL_HEADERS], rows: [] },
                        shiftsData: { headers: [...DEFAULT_SHIFTS_HEADERS], rows: [] },
                        dayName: '',
                        hijriDate: '',
                        gregorianDate: '',
                        receiptNumber: ''
                    };

                    await fetch('/duties/api/save-draft', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                        },
                        body: JSON.stringify(emptyData)
                    });

                    console.log('✅ تم حفظ بيانات فارغة في قاعدة البيانات');
                } else {
                    console.warn('⚠️ فشل في مسح المسودة من قاعدة البيانات');
                }
            } catch (error) {
                console.warn('⚠️ خطأ في مسح المسودة من قاعدة البيانات:', error);
            }

            // إعادة تعيين البيانات
            dutyData.rows = [];
            patrolData.rows = [];
            shiftsData.rows = [];

            // مسح الحقول
            document.getElementById('dayName').value = '';
            document.getElementById('hijriDate').value = '';
            document.getElementById('gregorianDate').value = '';
            document.getElementById('receiptNumber').value = '';

            // إعادة إنشاء الجداول
            generateTable();
            generatePatrolTable();
            generateShiftsTable();

            // إعادة تهيئة التواريخ ورقم الكشف تلقائياً
            await initializeDutyReceipt();

            showNotification('✅ تم مسح جميع البيانات بنجاح', 'success');
            console.log('🗑️ تم مسح جميع البيانات بنجاح');

        } catch (error) {
            console.error('❌ خطأ في مسح البيانات:', error);
            showNotification('❌ حدث خطأ أثناء مسح البيانات', 'error');
        }
    }
}

// دالة تصدير إلى Excel (نفس نظام كشف الاستلامات)
async function exportToExcel() {
    try {
        console.log('🚀 بدء عملية التصدير...');

        // التحقق من وجود البيانات
        if (!dutyData || !dutyData.headers) {
            console.error('❌ بيانات كشف الواجبات غير موجودة');
            showNotification('لا توجد بيانات لتصديرها', 'warning');
            return;
        }

        // جمع معلومات الكشف
        const dayName = document.getElementById('dayName').value || '';
        const hijriDate = document.getElementById('hijriDate').value || '';
        const gregorianDate = document.getElementById('gregorianDate').value || '';
        const receiptNumber = document.getElementById('receiptNumber').value || '';

        console.log(`📋 معلومات الكشف: اليوم=${dayName}, هجري=${hijriDate}, ميلادي=${gregorianDate}, رقم=${receiptNumber}`);

        // تحضير بيانات الجداول مع تحويل الأرقام إلى أسماء وإضافة ترقيم صحيح
        const dutyTableData = {
            headers: dutyData.headers || [],
            rows: await convertRowsToNames(dutyData.rows || [], 'duty')
        };

        const patrolTableData = {
            headers: patrolData.headers || [],
            rows: await convertRowsToNames(patrolData.rows || [], 'patrol')
        };

        const shiftsTableData = {
            headers: shiftsData.headers || [],
            rows: await convertRowsToNames(shiftsData.rows || [], 'shifts')
        };

        console.log(`📊 ملخص البيانات: الواجبات (${dutyTableData.rows.length} صف), الدوريات (${patrolTableData.rows.length} صف), المناوبات (${shiftsTableData.rows.length} صف)`);

        // إعداد البيانات للإرسال
        const exportData = {
            duty_info: {
                day_name: dayName,
                hijri_date: hijriDate,
                gregorian_date: gregorianDate,
                receipt_number: receiptNumber
            },
            duty_table: dutyTableData,
            patrol_table: patrolTableData,
            shifts_table: shiftsTableData
        };

        console.log('📦 البيانات المعدة للإرسال:', exportData);

        // التحقق من وجود CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const headers = {
            'Content-Type': 'application/json'
        };

        if (csrfToken) {
            headers['X-CSRFToken'] = csrfToken.getAttribute('content');
        }

        console.log('🌐 إرسال البيانات للخادم...');

        // إرسال البيانات للخادم لتصدير Excel
        fetch('/duties/export-excel', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(exportData)
        })
        .then(response => {
            console.log('📡 استجابة الخادم:', response.status, response.statusText);
            if (response.ok) {
                return response.blob();
            }
            throw new Error(`فشل في تصدير الملف: ${response.status} ${response.statusText}`);
        })
        .then(blob => {
            console.log('📁 تم استلام الملف، حجم:', blob.size, 'بايت');

            // تحميل الملف
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;

            // اسم الملف
            const fileName = `كشف_الواجبات_${gregorianDate || new Date().toISOString().split('T')[0]}`;

            // تحديد امتداد الملف
            const fileExtension = blob.type.includes('excel') || blob.type.includes('spreadsheet') ? '.xlsx' : '.csv';
            a.download = fileName + fileExtension;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            console.log('✅ تم تحميل الملف بنجاح');

            // رسالة نجاح
            const successMessage = '📊 تم تصدير كشف الواجبات بنجاح';
            showNotification(successMessage, 'success', 4000);
        })
        .catch(error => {
            console.error('❌ خطأ في تصدير Excel:', error);
            showNotification('❌ حدث خطأ أثناء تصدير الملف: ' + error.message, 'error');
        });

    } catch (error) {
        console.error('❌ خطأ في إعداد بيانات التصدير:', error);
        showNotification('❌ حدث خطأ في إعداد البيانات للتصدير: ' + error.message, 'error');
    }
}

// دالة لتحويل الأرقام إلى أسماء في الصفوف
async function convertRowsToNames(rows, tableType) {
    const convertedRows = [];
    let validRowNumber = 1; // عداد للصفوف التي تحتوي على بيانات

    for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
        const row = rows[rowIndex];
        const convertedRow = [...row]; // نسخة من الصف

        // التحقق من وجود بيانات في الصف (غير العمود الأول)
        const hasData = convertedRow.slice(1).some(cell => cell && cell.toString().trim() !== '');

        if (hasData) {
            // وضع رقم صحيح للصفوف التي تحتوي على بيانات فقط
            convertedRow[0] = validRowNumber;
            validRowNumber++;
        } else {
            // إذا لم يكن هناك بيانات، تخطي هذا الصف
            continue;
        }

        // تحديد أعمدة المواقع والأفراد حسب نوع الجدول
        let locationCol, personnelStartCol, personnelEndCol;

        switch (tableType) {
            case 'duty':
                locationCol = 1;
                personnelStartCol = 2;
                personnelEndCol = 7;
                break;
            case 'patrol':
                locationCol = 1;
                personnelStartCol = 2;
                personnelEndCol = 5;
                break;
            case 'shifts':
                locationCol = 1;
                personnelStartCol = 2;
                personnelEndCol = 4;
                break;
            default:
                convertedRows.push(convertedRow);
                continue;
        }

        // تحويل الموقع من رقم إلى اسم
        if (convertedRow[locationCol]) {
            const locationName = await getLocationNameById(convertedRow[locationCol]);
            if (locationName) {
                convertedRow[locationCol] = locationName;
            }
        }

        // تحويل الأفراد من أرقام إلى أسماء
        for (let col = personnelStartCol; col <= personnelEndCol; col++) {
            if (convertedRow[col]) {
                const personnelName = await getPersonnelNameById(convertedRow[col]);
                if (personnelName) {
                    convertedRow[col] = personnelName;
                }
            }
        }

        convertedRows.push(convertedRow);
    }

    return convertedRows;
}

// دالة للحصول على اسم الموقع بالرقم
async function getLocationNameById(locationId) {
    try {
        // البحث في المواقع المحملة مسبقاً
        if (window.locations) {
            const location = window.locations.find(loc => loc.id == locationId);
            return location ? location.name : locationId;
        }

        // إذا لم تكن المواقع محملة، جلبها من الخادم
        const response = await fetch('/duties/api/get-locations');
        if (response.ok) {
            const data = await response.json();
            if (data.success && data.locations) {
                const location = data.locations.find(loc => loc.id == locationId);
                return location ? location.name : locationId;
            }
        }

        return locationId; // إرجاع الرقم إذا فشل الحصول على الاسم
    } catch (error) {
        console.error('❌ خطأ في الحصول على اسم الموقع:', error);
        return locationId;
    }
}

// دالة للحصول على اسم الفرد بالرقم
async function getPersonnelNameById(personnelId) {
    try {
        // البحث في الأفراد المحملين مسبقاً
        for (const locationId in locationPersonnelMap) {
            const personnel = locationPersonnelMap[locationId];
            const person = personnel.find(p => p.id == personnelId);
            if (person) {
                return person.display_name || `${person.name} (${person.rank})`;
            }
        }

        // إذا لم يوجد، محاولة جلبه من الخادم
        const response = await fetch(`/duties/api/get-personnel/${personnelId}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success && data.personnel) {
                const person = data.personnel;
                return person.display_name || `${person.name} (${person.rank})`;
            }
        }

        return personnelId; // إرجاع الرقم إذا فشل الحصول على الاسم
    } catch (error) {
        console.error('❌ خطأ في الحصول على اسم الفرد:', error);
        return personnelId;
    }
}

// تهيئة كشف الواجبات تلقائياً (مثل كشف الاستلامات)
async function initializeDutyReceipt() {
    try {
        console.log('🚀 بدء تهيئة كشف الواجبات...');

        // استخدم الطريقة التقليدية مباشرة (أكثر موثوقية)
        await initializeDutyReceiptManually();
    } catch (error) {
        console.error('❌ خطأ في تهيئة كشف الواجبات من API:', error);
        // في حالة فشل API، استخدم الطريقة التقليدية
        await initializeDutyReceiptManually();
    }
}

// تهيئة كشف الواجبات يدوياً (نسخة احتياطية)
async function initializeDutyReceiptManually() {
    console.log('📝 تهيئة كشف الواجبات يدوياً...');

    const now = new Date();

    // تحديث اليوم
    const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    const dayName = dayNames[now.getDay()];
    document.getElementById('dayName').value = dayName;

    // تحديث التاريخ الميلادي
    const gregorianDate = now.toISOString().split('T')[0];
    document.getElementById('gregorianDate').value = gregorianDate;

    // تحديث التاريخ الهجري فوراً
    const hijriDate = await getCurrentHijriDate();
    document.getElementById('hijriDate').value = hijriDate;

    // رقم الكشف التلقائي
    const receiptNumber = generateDutyReceiptNumber();
    document.getElementById('receiptNumber').value = receiptNumber;

    console.log('✅ تم تهيئة كشف الواجبات يدوياً:', {
        dayName,
        gregorianDate,
        hijriDate,
        receiptNumber
    });
}

// الحصول على التاريخ الهجري الحالي من API
async function getCurrentHijriDate() {
    try {
        const response = await fetch('/reports/api/hijri-date');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                console.log('✅ تم الحصول على التاريخ الهجري من API:', data.hijri_formatted);
                return data.hijri_formatted;
            }
        }
        throw new Error('فشل في الحصول على التاريخ من API');
    } catch (error) {
        console.error('خطأ في الحصول على التاريخ الهجري:', error);
        // استخدم الحساب البديل
        return fallbackHijriCalculation(new Date());
    }
}

// حساب بديل للتاريخ الهجري (محدث للتاريخ الصحيح)
function fallbackHijriCalculation(date) {
    // التاريخ المرجعي الصحيح: 17 يوليو 2025 = 22 محرم 1447هـ
    const baseGregorianDate = new Date('2025-07-17');
    const baseHijriDay = 22;
    const baseHijriMonth = 1; // محرم (الشهر الأول)
    const baseHijriYear = 1447;

    const daysDiff = Math.floor((date - baseGregorianDate) / (1000 * 60 * 60 * 24));

    const hijriMonths = [
        'محرم', 'صفر', 'ربيع الأول', 'ربيع الثاني', 'جمادى الأولى', 'جمادى الثانية',
        'رجب', 'شعبان', 'رمضان', 'شوال', 'ذو القعدة', 'ذو الحجة'
    ];

    // أطوال الشهور الهجرية (تقريبية)
    const monthLengths = [30, 29, 30, 29, 30, 29, 30, 29, 30, 29, 30, 29];

    let hijriDay = baseHijriDay + daysDiff;
    let hijriMonth = baseHijriMonth;
    let hijriYear = baseHijriYear;

    // تعديل الأيام والشهور
    while (hijriDay > monthLengths[hijriMonth - 1]) {
        hijriDay -= monthLengths[hijriMonth - 1];
        hijriMonth++;
        if (hijriMonth > 12) {
            hijriMonth = 1;
            hijriYear++;
        }
    }

    while (hijriDay < 1) {
        hijriMonth--;
        if (hijriMonth < 1) {
            hijriMonth = 12;
            hijriYear--;
        }
        hijriDay += monthLengths[hijriMonth - 1];
    }

    return `${hijriDay.toString().padStart(2, '0')} ${hijriMonths[hijriMonth - 1]} ${hijriYear}هـ`;
}

// تحديث التواريخ تلقائياً باستخدام API
async function updateDatesAutomatically() {
    try {
        // الحصول على التاريخ والوقت من API
        const response = await fetch('/reports/api/hijri-date');
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                // تحديث اليوم
                const dayElement = document.getElementById('dayName');
                if (dayElement) {
                    dayElement.value = data.day_name;
                }

                // تحديث التاريخ الميلادي
                const gregorianElement = document.getElementById('gregorianDate');
                if (gregorianElement) {
                    gregorianElement.value = data.gregorian_formatted;
                }

                // تحديث التاريخ الهجري
                const hijriElement = document.getElementById('hijriDate');
                if (hijriElement) {
                    hijriElement.value = data.hijri_formatted;
                }

                console.log('✅ تم تحديث التواريخ تلقائياً:', {
                    dayName: data.day_name,
                    gregorianDate: data.gregorian_formatted,
                    hijriDate: data.hijri_formatted
                });
            }
        }
    } catch (error) {
        console.error('❌ خطأ في تحديث التواريخ تلقائياً:', error);
    }
}

// إعداد مستمعي أحداث التواريخ
function setupDateEventListeners() {
    // تحديث التاريخ الهجري عند تغيير التاريخ الميلادي
    const gregorianElement = document.getElementById('gregorianDate');
    if (gregorianElement) {
        gregorianElement.addEventListener('change', async function() {
            const date = new Date(this.value);
            const hijriDate = await convertToHijri(date);
            const hijriElement = document.getElementById('hijriDate');
            if (hijriElement) {
                hijriElement.value = hijriDate;
            }
            console.log('✅ تم تحديث التاريخ الهجري عند تغيير التاريخ الميلادي:', hijriDate);
        });
    }

    // حفظ تلقائي عند تغيير التاريخ الهجري
    const hijriElement = document.getElementById('hijriDate');
    if (hijriElement) {
        hijriElement.addEventListener('input', function() {
            console.log('📝 تم تغيير التاريخ الهجري:', this.value);
        });
    }
}

// تحويل التاريخ إلى هجري باستخدام API (دقيق ومعتمد على تقويم أم القرى)
async function convertToHijri(date) {
    try {
        // إذا كان التاريخ هو اليوم، استخدم API للحصول على التاريخ الحالي
        const today = new Date();
        const isToday = date.toDateString() === today.toDateString();

        if (isToday) {
            return await getCurrentHijriDate();
        }

        // للتواريخ الأخرى، استخدم الحساب البديل
        return fallbackHijriCalculation(date);
    } catch (error) {
        console.error('خطأ في تحويل التاريخ إلى هجري:', error);
        return fallbackHijriCalculation(date);
    }
}

// توليد رقم كشف تلقائي
function generateDutyReceiptNumber() {
    const now = new Date();
    return `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`;
}

// إضافة عمود جديد لجدول المناوبين
function addShiftsColumn() {
    const newColumnName = prompt('أدخل اسم العمود الجديد:');
    if (newColumnName && newColumnName.trim()) {
        shiftsData.headers.splice(-1, 0, newColumnName.trim());
        shiftsData.rows.forEach(row => {
            row.splice(-1, 0, '');
        });
        generateShiftsTable();
    }
}

// دوال التفريغ
function resetHeaders() {
    if (confirm('هل أنت متأكد من تفريغ الكشف؟')) {
        // تسجيل وقت التفريغ لمنع إعادة التحميل
        localStorage.setItem('dutyDataCleared', Date.now().toString());

        dutyData.headers = [...DEFAULT_HEADERS];
        dutyData.rows = [];
        generateTable();

        // حفظ التغييرات
        saveDataToLocalStorage();
    }
}

function resetPatrolTable() {
    if (confirm('هل أنت متأكد من تفريغ جدول الدوريات؟')) {
        // تسجيل وقت التفريغ لمنع إعادة التحميل
        localStorage.setItem('dutyDataCleared', Date.now().toString());

        patrolData.headers = [...DEFAULT_PATROL_HEADERS];
        patrolData.rows = [];
        generatePatrolTable();

        // حفظ التغييرات
        saveDataToLocalStorage();
    }
}

function resetShiftsTable() {
    if (confirm('هل أنت متأكد من تفريغ جدول المناوبين؟')) {
        // تسجيل وقت التفريغ لمنع إعادة التحميل
        localStorage.setItem('dutyDataCleared', Date.now().toString());

        shiftsData.headers = [...DEFAULT_SHIFTS_HEADERS];
        shiftsData.rows = [];
        generateShiftsTable();

        // حفظ التغييرات
        saveDataToLocalStorage();
    }
}

// دوال الحفظ في قاعدة البيانات

// حفظ بيانات الجدول الرئيسي في قاعدة البيانات
function saveDutyDataToServer() {
    const dataToSave = {
        dutyData: {
            headers: dutyData.headers,
            rows: dutyData.rows
        },
        patrolData: {
            headers: patrolData.headers,
            rows: patrolData.rows
        },
        shiftsData: {
            headers: shiftsData.headers,
            rows: shiftsData.rows
        },
        timestamp: new Date().toISOString()
    };

    console.log('💾 حفظ بيانات كشف الواجبات:', {
        dutyHeaders: dutyData.headers.length,
        dutyRows: dutyData.rows.length,
        patrolHeaders: patrolData.headers.length,
        patrolRows: patrolData.rows.length,
        shiftsHeaders: shiftsData.headers.length,
        shiftsRows: shiftsData.rows.length
    });

    // طباعة عينة من البيانات للتحقق
    console.log('📋 عينة من بيانات الجدول الرئيسي:', dutyData.rows.slice(0, 2));

    fetch('/duties/api/save-duty-data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify(dataToSave)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ تم حفظ بيانات كشف الواجبات في قاعدة البيانات');
        } else {
            console.error('❌ خطأ في حفظ بيانات كشف الواجبات:', data.error);
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الاتصال بالخادم:', error);
    });
}

// حفظ بيانات جدول الدوريات في قاعدة البيانات
function savePatrolDataToServer() {
    const dataToSave = {
        headers: patrolData.headers,
        rows: patrolData.rows,
        timestamp: new Date().toISOString()
    };

    fetch('/duties/api/save-patrol-data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify(dataToSave)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ تم حفظ بيانات جدول الدوريات في قاعدة البيانات');
        } else {
            console.error('❌ خطأ في حفظ بيانات جدول الدوريات:', data.error);
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الاتصال بالخادم:', error);
    });
}

// حفظ بيانات جدول المناوبين في قاعدة البيانات
function saveShiftsDataToServer() {
    const dataToSave = {
        headers: shiftsData.headers,
        rows: shiftsData.rows,
        timestamp: new Date().toISOString()
    };

    fetch('/duties/api/save-shifts-data', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify(dataToSave)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ تم حفظ بيانات جدول المناوبين في قاعدة البيانات');
        } else {
            console.error('❌ خطأ في حفظ بيانات جدول المناوبين:', data.error);
        }
    })
    .catch(error => {
        console.error('❌ خطأ في الاتصال بالخادم:', error);
    });
}

// دوال التحميل من قاعدة البيانات

// تحميل بيانات الجدول الرئيسي من قاعدة البيانات
async function loadDutyDataFromServer() {
    try {
        console.log('🔄 تحميل بيانات كشف الواجبات من قاعدة البيانات...');

        const response = await fetch('/duties/api/load-duty-data', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });

        const result = await response.json();

        if (result.success && result.data) {
            console.log('✅ تم تحميل بيانات كشف الواجبات من قاعدة البيانات');
            console.log('📦 البيانات المستلمة:', result.data);

            // استخراج البيانات
            if (result.data.dutyData && result.data.dutyData.headers && result.data.dutyData.rows) {
                dutyData.headers = result.data.dutyData.headers;
                dutyData.rows = result.data.dutyData.rows;
                console.log('📋 تم تحميل بيانات الجدول الرئيسي:', dutyData.headers.length, 'أعمدة،', dutyData.rows.length, 'صفوف');
                console.log('📋 عينة من البيانات:', dutyData.rows.slice(0, 2));
            }

            if (result.data.patrolData && result.data.patrolData.headers && result.data.patrolData.rows) {
                patrolData.headers = result.data.patrolData.headers;
                patrolData.rows = result.data.patrolData.rows;
                console.log('🚶 تم تحميل بيانات جدول الدوريات:', patrolData.headers.length, 'أعمدة،', patrolData.rows.length, 'صفوف');
            }

            if (result.data.shiftsData && result.data.shiftsData.headers && result.data.shiftsData.rows) {
                shiftsData.headers = result.data.shiftsData.headers;
                shiftsData.rows = result.data.shiftsData.rows;
                console.log('👥 تم تحميل بيانات جدول المناوبين:', shiftsData.headers.length, 'أعمدة،', shiftsData.rows.length, 'صفوف');
            }

            // إعادة إنشاء الجداول
            generateTable();
            generatePatrolTable();
            generateShiftsTable();

            // انتظار قصير للتأكد من إنشاء الجداول ثم إعادة تطبيق الأفراد
            setTimeout(async () => {
                await reapplyPersonnelSelections();
            }, 500);

            return true;
        } else {
            console.log('ℹ️ لا توجد بيانات محفوظة في قاعدة البيانات');

            // إنشاء صف افتراضي إذا لم توجد بيانات
            let needsSave = false;
            if (dutyData.rows.length === 0) {
                console.log('📋 إنشاء صف افتراضي للجدول الرئيسي');
                dutyData.rows.push(Array(dutyData.headers.length).fill(''));
                needsSave = true;
            }
            if (patrolData.rows.length === 0) {
                console.log('🚶 إنشاء صف افتراضي لجدول الدوريات');
                patrolData.rows.push(Array(patrolData.headers.length).fill(''));
                needsSave = true;
            }
            if (shiftsData.rows.length === 0) {
                console.log('👥 إنشاء صف افتراضي لجدول المناوبين');
                shiftsData.rows.push(Array(shiftsData.headers.length).fill(''));
                needsSave = true;
            }

            // حفظ البيانات الافتراضية إذا تم إنشاؤها
            if (needsSave) {
                console.log('💾 حفظ البيانات الافتراضية في قاعدة البيانات');
                setTimeout(() => saveDutyDataToServer(), 500);
            }

            return false;
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل بيانات كشف الواجبات:', error);
        return false;
    }
}

// تحميل بيانات جدول الدوريات من قاعدة البيانات
async function loadPatrolDataFromServer() {
    try {
        console.log('🔄 تحميل بيانات جدول الدوريات من قاعدة البيانات...');

        const response = await fetch('/duties/api/load-patrol-data', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });

        const result = await response.json();

        if (result.success && result.data && result.data.headers && result.data.rows) {
            console.log('✅ تم تحميل بيانات جدول الدوريات من قاعدة البيانات');

            patrolData.headers = result.data.headers;
            patrolData.rows = result.data.rows;
            console.log('🚶 جدول الدوريات:', patrolData.headers.length, 'أعمدة،', patrolData.rows.length, 'صفوف');

            // إعادة إنشاء جدول الدوريات
            generatePatrolTable();

            return true;
        } else {
            console.log('ℹ️ لا توجد بيانات محفوظة لجدول الدوريات');
            return false;
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل بيانات جدول الدوريات:', error);
        return false;
    }
}

// تحميل بيانات جدول المناوبين من قاعدة البيانات
async function loadShiftsDataFromServer() {
    try {
        console.log('🔄 تحميل بيانات جدول المناوبين من قاعدة البيانات...');

        const response = await fetch('/duties/api/load-shifts-data', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            }
        });

        const result = await response.json();

        if (result.success && result.data && result.data.headers && result.data.rows) {
            console.log('✅ تم تحميل بيانات جدول المناوبين من قاعدة البيانات');

            shiftsData.headers = result.data.headers;
            shiftsData.rows = result.data.rows;
            console.log('👥 جدول المناوبين:', shiftsData.headers.length, 'أعمدة،', shiftsData.rows.length, 'صفوف');

            // إعادة إنشاء جدول المناوبين
            generateShiftsTable();

            return true;
        } else {
            console.log('ℹ️ لا توجد بيانات محفوظة لجدول المناوبين');
            return false;
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل بيانات جدول المناوبين:', error);
        return false;
    }
}

// دالة إعادة تطبيق اختيارات الأفراد بعد تحميل البيانات
async function reapplyPersonnelSelections() {
    console.log('🔄 إعادة تطبيق اختيارات الأفراد...');

    // التحقق من وجود الجداول
    const dutyTable = document.getElementById('dutyTable');
    if (!dutyTable) {
        console.log('⚠️ جدول الواجبات غير موجود، سيتم المحاولة لاحقاً');
        setTimeout(reapplyPersonnelSelections, 1000);
        return;
    }

    // إعادة تطبيق الأفراد في الجدول الرئيسي
    for (let rowIndex = 0; rowIndex < dutyData.rows.length; rowIndex++) {
        const row = dutyData.rows[rowIndex];
        const locationId = row[1]; // عمود الموقع

        if (locationId) {
            console.log(`🔄 إعادة تحميل أفراد الموقع ${locationId} للصف ${rowIndex}`);
            try {
                await loadPersonnelForLocation(locationId, rowIndex);
                // انتظار قصير بين كل صف
                await new Promise(resolve => setTimeout(resolve, 200));
            } catch (error) {
                console.error(`❌ خطأ في إعادة تحميل أفراد الصف ${rowIndex}:`, error);
            }
        }
    }

    // إعادة تطبيق الأفراد في جدول الدوريات
    for (let rowIndex = 0; rowIndex < patrolData.rows.length; rowIndex++) {
        const row = patrolData.rows[rowIndex];
        const locationId = row[1]; // عمود الموقع

        if (locationId) {
            console.log(`🔄 إعادة تحميل أفراد الموقع ${locationId} لدورية الصف ${rowIndex}`);
            await loadPersonnelForPatrolLocation(locationId, rowIndex);
        }
    }

    // إعادة تطبيق الأفراد في جدول المناوبين
    for (let rowIndex = 0; rowIndex < shiftsData.rows.length; rowIndex++) {
        const row = shiftsData.rows[rowIndex];
        const locationId = row[1]; // عمود الموقع

        if (locationId) {
            console.log(`🔄 إعادة تحميل أفراد الموقع ${locationId} لمناوبة الصف ${rowIndex}`);
            await loadPersonnelForShiftsLocation(locationId, rowIndex);
        }
    }

    console.log('✅ تم إعادة تطبيق جميع اختيارات الأفراد');
}

console.log('✅ تم تحميل ملف duties-simple.js بنجاح');
