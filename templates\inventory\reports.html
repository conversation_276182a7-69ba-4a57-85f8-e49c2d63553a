{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>
            <i class="fas fa-chart-bar"></i> تقارير المخزون
        </h3>
        <p class="text-muted">تقارير شاملة عن حالة المخزون والأصناف</p>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right"></i> العودة للمخزون
        </a>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ low_stock_items|length }}</h4>
                        <p class="mb-0">أصناف منخفضة المخزون</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ expired_items|length }}</h4>
                        <p class="mb-0">أصناف منتهية الصلاحية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-times fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_value|round(2) }}</h4>
                        <p class="mb-0">القيمة الإجمالية (ريال)</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ warehouses|length }}</h4>
                        <p class="mb-0">المستودعات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-warehouse fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تقرير الأصناف منخفضة المخزون -->
{% if low_stock_items %}
<div class="card mb-4">
    <div class="card-header bg-warning text-white">
        <h5 class="mb-0">
            <i class="fas fa-exclamation-triangle"></i> الأصناف منخفضة المخزون
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>اسم الصنف</th>
                        <th>الفئة</th>
                        <th>المستودع</th>
                        <th>الكمية الحالية</th>
                        <th>الحد الأدنى</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in low_stock_items %}
                    <tr>
                        <td>{{ item.name }}</td>
                        <td>{{ item.category }}</td>
                        <td>{{ item.warehouse.name if item.warehouse else 'غير محدد' }}</td>
                        <td>
                            <span class="badge badge-warning">{{ item.quantity_in_stock }}</span>
                        </td>
                        <td>{{ item.minimum_stock }}</td>
                        <td>
                            {% if item.quantity_in_stock == 0 %}
                                <span class="badge badge-danger">نفد المخزون</span>
                            {% else %}
                                <span class="badge badge-warning">منخفض</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{{ url_for('inventory.item_details', item_id=item.id) }}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i> عرض
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

<!-- تقرير الأصناف منتهية الصلاحية -->
{% if expired_items %}
<div class="card mb-4">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">
            <i class="fas fa-calendar-times"></i> الأصناف منتهية الصلاحية
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>اسم الصنف</th>
                        <th>الفئة</th>
                        <th>المستودع</th>
                        <th>الكمية</th>
                        <th>تاريخ انتهاء الصلاحية</th>
                        <th>منتهية منذ</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in expired_items %}
                    <tr>
                        <td>{{ item.name }}</td>
                        <td>{{ item.category }}</td>
                        <td>{{ item.warehouse.name if item.warehouse else 'غير محدد' }}</td>
                        <td>{{ item.quantity_in_stock }}</td>
                        <td>{{ item.expiry_date.strftime('%Y-%m-%d') if item.expiry_date else 'غير محدد' }}</td>
                        <td>
                            {% if item.expiry_date %}
                                {% set days_expired = (moment().date() - item.expiry_date.date()).days %}
                                <span class="badge badge-danger">{{ days_expired }} يوم</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{{ url_for('inventory.item_details', item_id=item.id) }}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i> عرض
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

<!-- تقرير المستودعات -->
{% if warehouses %}
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">
            <i class="fas fa-warehouse"></i> تقرير المستودعات
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            {% for warehouse in warehouses %}
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">{{ warehouse.name }}</h6>
                    </div>
                    <div class="card-body">
                        {% set warehouse_items = warehouse.inventory_items %}
                        {% set total_items = warehouse_items.count() %}
                        {% set low_stock_count = warehouse_items.filter(warehouse_items.c.quantity_in_stock <= warehouse_items.c.minimum_stock).count() %}
                        
                        <p class="mb-1">
                            <strong>إجمالي الأصناف:</strong> {{ total_items }}
                        </p>
                        <p class="mb-1">
                            <strong>أصناف منخفضة:</strong> 
                            <span class="badge badge-warning">{{ low_stock_count }}</span>
                        </p>
                        <p class="mb-0">
                            <strong>الموقع:</strong> {{ warehouse.location or 'غير محدد' }}
                        </p>
                    </div>
                    <div class="card-footer">
                        <a href="{{ url_for('warehouse.details', warehouse_id=warehouse.id) }}" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i> عرض التفاصيل
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- أزرار التصدير -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-download"></i> تصدير التقارير
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 mb-2">
                <a href="#" class="btn btn-success btn-block" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> تصدير إلى Excel
                </a>
            </div>
            <div class="col-md-4 mb-2">
                <a href="#" class="btn btn-danger btn-block" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf"></i> تصدير إلى PDF
                </a>
            </div>
            <div class="col-md-4 mb-2">
                <a href="#" class="btn btn-info btn-block" onclick="printReport()">
                    <i class="fas fa-print"></i> طباعة التقرير
                </a>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
function exportToExcel() {
    // يمكن تنفيذ تصدير Excel هنا
    alert('سيتم تنفيذ تصدير Excel قريباً');
}

function exportToPDF() {
    // يمكن تنفيذ تصدير PDF هنا
    alert('سيتم تنفيذ تصدير PDF قريباً');
}

function printReport() {
    window.print();
}
</script>
{% endblock %}
