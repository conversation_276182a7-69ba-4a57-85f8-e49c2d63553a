{% extends "base.html" %}

{% block title %}كشف الاستلامات المنفصل{% endblock %}

{% block head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/receipts-isolated.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h3 style="color: var(--text-primary) !important;">
                <i class="fas fa-clipboard-list"></i> كشف الاستلامات (منفصل)
            </h3>
            <p class="text-muted">إنشاء وإدارة كشوف استلام المناوبات - نسخة منفصلة عن كشف الواجبات</p>
        </div>
        <div class="col-md-4 text-right">
            <button class="receipts-btn receipts-btn-success" onclick="saveAllReceiptsData()">
                <i class="fas fa-save"></i> حفظ البيانات
            </button>
            <button class="receipts-btn receipts-btn-primary" onclick="printReceipt()">
                <i class="fas fa-print"></i> طباعة
            </button>
        </div>
    </div>

    <!-- Receipt Info Section -->
    <div class="receipts-table-container mb-4">
        <h4 class="receipts-table-title">معلومات الكشف</h4>
        <div class="p-3">
            <div class="row">
                <div class="col-md-3">
                    <div class="receipts-form-group">
                        <label class="receipts-form-label">التاريخ الهجري</label>
                        <input type="text" id="hijriDate" class="receipts-form-control" readonly>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="receipts-form-group">
                        <label class="receipts-form-label">التاريخ الميلادي</label>
                        <input type="date" id="gregorianDate" class="receipts-form-control">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="receipts-form-group">
                        <label class="receipts-form-label">رقم الكشف</label>
                        <input type="text" id="receiptNumber" class="receipts-form-control">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="receipts-form-group">
                        <label class="receipts-form-label">اليوم</label>
                        <input type="text" id="dayName" class="receipts-form-control" readonly>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Patrol Table Section -->
    <div class="receipts-table-container mb-4">
        <h4 class="receipts-table-title">كشف استلامات الدوريات</h4>
        <div class="table-responsive">
            <table class="receipts-table" id="patrolTable">
                <thead id="patrolTableHeader">
                    <!-- سيتم إنشاؤها بواسطة JavaScript -->
                </thead>
                <tbody id="patrolTableBody">
                    <!-- سيتم إنشاؤها بواسطة JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Shifts Table Section -->
    <div class="receipts-table-container mb-4">
        <h4 class="receipts-table-title">كشف المناوبين</h4>
        <div class="table-responsive">
            <table class="receipts-table" id="shiftsTable">
                <thead id="shiftsTableHeader">
                    <!-- سيتم إنشاؤها بواسطة JavaScript -->
                </thead>
                <tbody id="shiftsTableBody">
                    <!-- سيتم إنشاؤها بواسطة JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Notes Section -->
    <div class="receipts-table-container mb-4">
        <h4 class="receipts-table-title">الملاحظات</h4>
        <div class="p-3">
            <div class="row">
                <div class="col-md-6">
                    <div class="receipts-form-group">
                        <label class="receipts-form-label">ملاحظات الدوريات</label>
                        <textarea id="patrolNotes" class="receipts-form-control" rows="4" 
                                  placeholder="أدخل ملاحظات الدوريات هنا..."></textarea>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="receipts-form-group">
                        <label class="receipts-form-label">ملاحظات الحراس</label>
                        <textarea id="guardNotes" class="receipts-form-control" rows="4" 
                                  placeholder="أدخل ملاحظات الحراس هنا..."></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Signatures Section -->
    <div class="receipts-table-container mb-4">
        <h4 class="receipts-table-title">التوقيعات</h4>
        <div class="p-3">
            <div class="row">
                <div class="col-md-4">
                    <div class="receipts-form-group">
                        <label class="receipts-form-label">اسم الحارس</label>
                        <input type="text" id="guardName" class="receipts-form-control" 
                               placeholder="أدخل اسم الحارس">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="receipts-form-group">
                        <label class="receipts-form-label">المشرف</label>
                        <input type="text" id="supervisor" class="receipts-form-control" 
                               placeholder="أدخل اسم المشرف">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="receipts-form-group">
                        <label class="receipts-form-label">المعتمد</label>
                        <input type="text" id="approvedBy" class="receipts-form-control" 
                               placeholder="أدخل اسم المعتمد">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="text-center mb-4">
        <button class="receipts-btn receipts-btn-success me-2" onclick="saveAllReceiptsData()">
            <i class="fas fa-save"></i> حفظ جميع البيانات
        </button>
        <button class="receipts-btn receipts-btn-primary me-2" onclick="loadAllReceiptsData()">
            <i class="fas fa-download"></i> تحميل البيانات
        </button>
        <button class="receipts-btn receipts-btn-primary me-2" onclick="printReceipt()">
            <i class="fas fa-print"></i> طباعة الكشف
        </button>
        <button class="receipts-btn receipts-btn-primary" onclick="exportToExcel()">
            <i class="fas fa-file-excel"></i> تصدير Excel
        </button>
    </div>
</div>

<!-- Status Messages -->
<div id="statusMessages" class="position-fixed" style="top: 20px; right: 20px; z-index: 1050;">
    <!-- سيتم إضافة الرسائل هنا بواسطة JavaScript -->
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/receipts-isolated.js') }}"></script>

<script>
// دوال إضافية للواجهة
function printReceipt() {
    window.print();
}

function exportToExcel() {
    // TODO: تنفيذ تصدير Excel
    showMessage('سيتم تنفيذ تصدير Excel قريباً', 'info');
}

function loadAllReceiptsData() {
    console.log('🔄 تحميل جميع بيانات كشف الاستلامات...');
    
    // تحميل البيانات الرئيسية
    fetch('/receipts-isolated/api/get-main-data')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تطبيق البيانات الرئيسية
                if (data.data.hijriDate) document.getElementById('hijriDate').value = data.data.hijriDate;
                if (data.data.gregorianDate) document.getElementById('gregorianDate').value = data.data.gregorianDate;
                if (data.data.receiptNumber) document.getElementById('receiptNumber').value = data.data.receiptNumber;
                
                showMessage('تم تحميل البيانات الرئيسية بنجاح', 'success');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل البيانات الرئيسية:', error);
        });
    
    // تحميل بيانات الدوريات
    fetch('/receipts-isolated/api/get-patrol-data')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                receiptsPatrolData = { ...receiptsPatrolData, ...data.data };
                generateReceiptsPatrolTable();
                showMessage('تم تحميل بيانات الدوريات بنجاح', 'success');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل بيانات الدوريات:', error);
        });
    
    // تحميل بيانات المناوبين
    fetch('/receipts-isolated/api/get-shifts-data')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                receiptsShiftsData = { ...receiptsShiftsData, ...data.shiftsData };
                generateReceiptsShiftsTable();
                showMessage('تم تحميل بيانات المناوبين بنجاح', 'success');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في تحميل بيانات المناوبين:', error);
        });
}

function showMessage(message, type = 'info') {
    const messagesContainer = document.getElementById('statusMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `receipts-alert receipts-alert-${type} receipts-fade-in`;
    messageDiv.textContent = message;
    
    messagesContainer.appendChild(messageDiv);
    
    // إزالة الرسالة بعد 3 ثوان
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

// تهيئة التاريخ عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    
    // تحديث التاريخ الميلادي
    document.getElementById('gregorianDate').value = now.toISOString().split('T')[0];
    
    // تحديث اليوم
    const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    document.getElementById('dayName').value = dayNames[now.getDay()];
    
    // رقم الكشف التلقائي
    document.getElementById('receiptNumber').value = 'R' + now.getFullYear() + (now.getMonth() + 1).toString().padStart(2, '0') + now.getDate().toString().padStart(2, '0') + '-' + Math.floor(Math.random() * 1000).toString().padStart(3, '0');
});
</script>
{% endblock %}
