#!/usr/bin/env python3
"""
سكريبت اختبار شامل لنظام كشف الواجبات
"""

import os
import sys
import json
from datetime import datetime, date, time

def test_database_models():
    """اختبار نماذج قاعدة البيانات"""
    print("🔍 اختبار نماذج قاعدة البيانات...")
    
    try:
        from app import create_app
        from models import DutyData, DutyPersonnel, DutyTemplate, Location, Personnel, db
        
        app = create_app()
        with app.app_context():
            
            # التحقق من وجود الجداول
            print("   📋 التحقق من وجود الجداول...")
            
            # اختبار إنشاء سجل واجب تجريبي
            test_location = Location.query.first()
            if not test_location:
                print("   ⚠️  لا توجد مواقع في النظام - سيتم إنشاء موقع تجريبي")
                test_location = Location(
                    name="موقع تجريبي للواجبات",
                    serial_number="TEST-DUTY-001",
                    type="مستودع",
                    description="موقع تجريبي لاختبار كشف الواجبات"
                )
                db.session.add(test_location)
                db.session.commit()
            
            # إنشاء واجب تجريبي
            test_duty = DutyData(
                location_id=test_location.id,
                user_id=1,
                duty_date=date.today(),
                duty_time=time(8, 0),
                duty_data=json.dumps({
                    'test': True,
                    'description': 'واجب تجريبي للاختبار'
                }, ensure_ascii=False),
                notes="واجب تجريبي لاختبار النظام"
            )
            
            db.session.add(test_duty)
            db.session.commit()
            
            print(f"   ✅ تم إنشاء واجب تجريبي برقم: {test_duty.id}")
            
            # اختبار قالب واجب
            test_template = DutyTemplate(
                name="قالب تجريبي",
                location_id=test_location.id,
                template_data=json.dumps({
                    'rows': [
                        {'position': 'حارس', 'status': 'حاضر'},
                        {'position': 'مراقب', 'status': 'حاضر'}
                    ],
                    'notes': 'قالب تجريبي'
                }, ensure_ascii=False),
                created_by=1
            )
            
            db.session.add(test_template)
            db.session.commit()
            
            print(f"   ✅ تم إنشاء قالب تجريبي برقم: {test_template.id}")
            
            # التحقق من العلاقات
            duty_with_location = db.session.query(DutyData, Location).join(
                Location, DutyData.location_id == Location.id
            ).filter(DutyData.id == test_duty.id).first()
            
            if duty_with_location:
                print("   ✅ العلاقات بين الجداول تعمل بشكل صحيح")
            else:
                print("   ❌ مشكلة في العلاقات بين الجداول")
                return False
            
            # تنظيف البيانات التجريبية
            db.session.delete(test_duty)
            db.session.delete(test_template)
            db.session.commit()
            
            print("   🧹 تم تنظيف البيانات التجريبية")
            
            return True
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار نماذج قاعدة البيانات: {str(e)}")
        return False

def test_api_endpoints():
    """اختبار API endpoints"""
    print("\n🌐 اختبار API endpoints...")
    
    try:
        from app import create_app
        from models import Location, Personnel, LocationPersonnel, db
        
        app = create_app()
        
        with app.test_client() as client:
            # محاكاة تسجيل الدخول
            with app.app_context():
                # التحقق من وجود موقع للاختبار
                test_location = Location.query.first()
                if not test_location:
                    test_location = Location(
                        name="موقع اختبار API",
                        serial_number="API-TEST-001",
                        type="مكتب",
                        description="موقع لاختبار API"
                    )
                    db.session.add(test_location)
                    db.session.commit()
            
            # اختبار الحصول على المواقع
            print("   📍 اختبار الحصول على المواقع...")
            response = client.get('/duties/api/get-locations')
            
            if response.status_code == 200:
                data = response.get_json()
                if data.get('success'):
                    print(f"   ✅ تم الحصول على {len(data['locations'])} موقع")
                else:
                    print(f"   ❌ فشل في الحصول على المواقع: {data.get('error')}")
                    return False
            else:
                print(f"   ❌ خطأ HTTP: {response.status_code}")
                return False
            
            # اختبار الحصول على أفراد الموقع
            print("   👥 اختبار الحصول على أفراد الموقع...")
            response = client.get(f'/duties/api/get-location-personnel/{test_location.id}')
            
            if response.status_code == 200:
                data = response.get_json()
                if data.get('success'):
                    print(f"   ✅ تم الحصول على {len(data['personnel'])} فرد للموقع")
                else:
                    print(f"   ⚠️  لا توجد أفراد معينين للموقع: {data.get('error')}")
            else:
                print(f"   ❌ خطأ HTTP: {response.status_code}")
                return False
            
            # اختبار حفظ واجب
            print("   💾 اختبار حفظ واجب...")
            duty_data = {
                'location_id': test_location.id,
                'duty_date': '2025-07-15',
                'duty_time': '08:00',
                'duty_data': [
                    {
                        'row_number': 1,
                        'personnel_id': '',
                        'personnel_name': 'فرد تجريبي',
                        'rank': 'جندي',
                        'position': 'حارس',
                        'status': 'حاضر',
                        'notes': 'اختبار'
                    }
                ],
                'personnel': [],
                'notes': 'واجب تجريبي للاختبار'
            }
            
            response = client.post('/duties/api/save-duty',
                                 json=duty_data,
                                 content_type='application/json')
            
            if response.status_code == 200:
                data = response.get_json()
                if data.get('success'):
                    duty_id = data.get('duty_id')
                    print(f"   ✅ تم حفظ الواجب برقم: {duty_id}")
                    
                    # اختبار الحصول على الواجب
                    print("   📋 اختبار الحصول على تفاصيل الواجب...")
                    response = client.get(f'/duties/api/get-duty/{duty_id}')
                    
                    if response.status_code == 200:
                        data = response.get_json()
                        if data.get('success'):
                            print("   ✅ تم الحصول على تفاصيل الواجب بنجاح")
                        else:
                            print(f"   ❌ فشل في الحصول على تفاصيل الواجب: {data.get('error')}")
                            return False
                    
                    # اختبار حذف الواجب
                    print("   🗑️  اختبار حذف الواجب...")
                    response = client.delete(f'/duties/api/delete-duty/{duty_id}')
                    
                    if response.status_code == 200:
                        data = response.get_json()
                        if data.get('success'):
                            print("   ✅ تم حذف الواجب بنجاح")
                        else:
                            print(f"   ❌ فشل في حذف الواجب: {data.get('error')}")
                            return False
                    
                else:
                    print(f"   ❌ فشل في حفظ الواجب: {data.get('error')}")
                    return False
            else:
                print(f"   ❌ خطأ HTTP في حفظ الواجب: {response.status_code}")
                return False
            
            return True
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار API: {str(e)}")
        return False

def test_templates_system():
    """اختبار نظام القوالب"""
    print("\n📝 اختبار نظام القوالب...")
    
    try:
        from app import create_app
        from models import DutyTemplate, Location, db
        
        app = create_app()
        
        with app.test_client() as client:
            with app.app_context():
                # الحصول على موقع للاختبار
                test_location = Location.query.first()
                if not test_location:
                    print("   ❌ لا توجد مواقع للاختبار")
                    return False
            
            # اختبار حفظ قالب
            print("   💾 اختبار حفظ قالب...")
            template_data = {
                'name': 'قالب اختبار API',
                'location_id': test_location.id,
                'template_data': {
                    'rows': [
                        {'position': 'حارس رئيسي', 'status': 'حاضر'},
                        {'position': 'حارس مساعد', 'status': 'حاضر'}
                    ],
                    'notes': 'قالب تجريبي للاختبار'
                }
            }
            
            response = client.post('/duties/api/save-template',
                                 json=template_data,
                                 content_type='application/json')
            
            if response.status_code == 200:
                data = response.get_json()
                if data.get('success'):
                    template_id = data.get('template_id')
                    print(f"   ✅ تم حفظ القالب برقم: {template_id}")
                    
                    # اختبار الحصول على القوالب
                    print("   📋 اختبار الحصول على قوالب الموقع...")
                    response = client.get(f'/duties/api/get-templates/{test_location.id}')
                    
                    if response.status_code == 200:
                        data = response.get_json()
                        if data.get('success'):
                            templates = data.get('templates', [])
                            print(f"   ✅ تم الحصول على {len(templates)} قالب")
                            
                            # البحث عن القالب المحفوظ
                            saved_template = next((t for t in templates if t['id'] == template_id), None)
                            if saved_template:
                                print("   ✅ تم العثور على القالب المحفوظ")
                            else:
                                print("   ❌ لم يتم العثور على القالب المحفوظ")
                                return False
                        else:
                            print(f"   ❌ فشل في الحصول على القوالب: {data.get('error')}")
                            return False
                    
                    # اختبار حذف القالب
                    print("   🗑️  اختبار حذف القالب...")
                    response = client.delete(f'/duties/api/delete-template/{template_id}')
                    
                    if response.status_code == 200:
                        data = response.get_json()
                        if data.get('success'):
                            print("   ✅ تم حذف القالب بنجاح")
                        else:
                            print(f"   ❌ فشل في حذف القالب: {data.get('error')}")
                            return False
                    
                else:
                    print(f"   ❌ فشل في حفظ القالب: {data.get('error')}")
                    return False
            else:
                print(f"   ❌ خطأ HTTP في حفظ القالب: {response.status_code}")
                return False
            
            return True
            
    except Exception as e:
        print(f"   ❌ خطأ في اختبار نظام القوالب: {str(e)}")
        return False

def test_page_access():
    """اختبار الوصول للصفحة"""
    print("\n🌐 اختبار الوصول لصفحة كشف الواجبات...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # اختبار الوصول للصفحة الرئيسية
            response = client.get('/duties/')
            
            if response.status_code == 200:
                print("   ✅ تم الوصول لصفحة كشف الواجبات بنجاح")
                
                # التحقق من وجود العناصر المهمة في الصفحة
                page_content = response.get_data(as_text=True)
                
                required_elements = [
                    'كشف الواجبات',
                    'dutyDate',
                    'dutyTime',
                    'locationSelect',
                    'dutyTable'
                ]
                
                missing_elements = []
                for element in required_elements:
                    if element not in page_content:
                        missing_elements.append(element)
                
                if missing_elements:
                    print(f"   ⚠️  عناصر مفقودة في الصفحة: {missing_elements}")
                else:
                    print("   ✅ جميع العناصر المطلوبة موجودة في الصفحة")
                
                return True
            else:
                print(f"   ❌ خطأ في الوصول للصفحة: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الوصول للصفحة: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار نظام كشف الواجبات")
    print("="*60)
    
    tests = [
        ("اختبار نماذج قاعدة البيانات", test_database_models),
        ("اختبار API endpoints", test_api_endpoints),
        ("اختبار نظام القوالب", test_templates_system),
        ("اختبار الوصول للصفحة", test_page_access)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ - {str(e)}")
    
    print(f"\n{'='*60}")
    print(f"📊 نتائج الاختبار:")
    print(f"   ✅ نجح: {passed_tests}/{total_tests}")
    print(f"   ❌ فشل: {total_tests - passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! نظام كشف الواجبات جاهز للاستخدام.")
        print("📋 الميزات المتاحة:")
        print("   - تسجيل التاريخ والوقت يدوياً")
        print("   - اختيار الموقع من القائمة")
        print("   - اختيار الأفراد المرتبطين بالموقع")
        print("   - حفظ واسترجاع الواجبات")
        print("   - نظام القوالب")
        print("   - سجل الواجبات مع التصفية")
        return 0
    else:
        print(f"\n⚠️ فشل {total_tests - passed_tests} اختبار. يرجى مراجعة الأخطاء.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
