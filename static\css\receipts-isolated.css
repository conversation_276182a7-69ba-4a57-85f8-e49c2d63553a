/* كشف الاستلامات المنفصل - CSS Styles */

/* تنسيق الجداول */
.receipts-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background-color: var(--bs-body-bg);
    border: 1px solid var(--bs-border-color);
}

.receipts-table th,
.receipts-table td {
    border: 1px solid var(--bs-border-color);
    padding: 8px;
    text-align: center;
    vertical-align: middle;
}

.receipts-table th {
    background-color: var(--bs-secondary-bg);
    font-weight: bold;
    color: var(--bs-body-color);
}

/* تنسيق خانات الإدخال */
.receipts-patrol-editable-cell,
.receipts-shifts-editable-cell {
    width: 100%;
    border: none;
    background: transparent;
    text-align: center;
    padding: 4px;
    font-size: 14px;
    color: var(--bs-body-color);
}

.receipts-patrol-editable-cell:focus,
.receipts-shifts-editable-cell:focus {
    outline: 2px solid var(--bs-primary);
    background-color: var(--bs-primary-bg-subtle);
}

/* تنسيق القوائم المنسدلة */
.receipts-patrol-location-select,
.receipts-shifts-location-select {
    width: 100%;
    border: none;
    background: transparent;
    text-align: center;
    padding: 4px;
    font-size: 14px;
    color: var(--bs-body-color);
}

.receipts-patrol-location-select:focus,
.receipts-shifts-location-select:focus {
    outline: 2px solid var(--bs-primary);
    background-color: var(--bs-primary-bg-subtle);
}

/* تنسيق أرقام الصفوف */
.row-number {
    font-weight: bold;
    color: var(--bs-primary);
    font-size: 16px;
}

/* تنسيق عناوين الجداول */
.receipts-table-title {
    background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark));
    color: white;
    padding: 15px;
    border-radius: 8px 8px 0 0;
    margin: 0;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
}

/* تنسيق حاويات الجداول */
.receipts-table-container {
    background-color: var(--bs-body-bg);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    overflow: hidden;
}

/* تنسيق الأزرار */
.receipts-btn {
    padding: 8px 16px;
    border-radius: 6px;
    border: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.receipts-btn-primary {
    background-color: var(--bs-primary);
    color: white;
}

.receipts-btn-primary:hover {
    background-color: var(--bs-primary-dark);
    transform: translateY(-1px);
}

.receipts-btn-success {
    background-color: var(--bs-success);
    color: white;
}

.receipts-btn-success:hover {
    background-color: var(--bs-success-dark);
    transform: translateY(-1px);
}

/* تنسيق النماذج */
.receipts-form-group {
    margin-bottom: 15px;
}

.receipts-form-label {
    font-weight: 600;
    color: var(--bs-body-color);
    margin-bottom: 5px;
    display: block;
}

.receipts-form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--bs-border-color);
    border-radius: 6px;
    background-color: var(--bs-body-bg);
    color: var(--bs-body-color);
    font-size: 14px;
}

.receipts-form-control:focus {
    outline: none;
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
}

/* تنسيق الرسائل */
.receipts-alert {
    padding: 12px 16px;
    border-radius: 6px;
    margin-bottom: 15px;
    border: 1px solid transparent;
}

.receipts-alert-success {
    background-color: var(--bs-success-bg-subtle);
    border-color: var(--bs-success-border-subtle);
    color: var(--bs-success-text-emphasis);
}

.receipts-alert-danger {
    background-color: var(--bs-danger-bg-subtle);
    border-color: var(--bs-danger-border-subtle);
    color: var(--bs-danger-text-emphasis);
}

.receipts-alert-warning {
    background-color: var(--bs-warning-bg-subtle);
    border-color: var(--bs-warning-border-subtle);
    color: var(--bs-warning-text-emphasis);
}

/* تنسيق متجاوب */
@media (max-width: 768px) {
    .receipts-table {
        font-size: 12px;
    }
    
    .receipts-table th,
    .receipts-table td {
        padding: 4px;
    }
    
    .receipts-patrol-editable-cell,
    .receipts-shifts-editable-cell,
    .receipts-patrol-location-select,
    .receipts-shifts-location-select {
        font-size: 12px;
        padding: 2px;
    }
    
    .receipts-table-title {
        font-size: 16px;
        padding: 10px;
    }
}

/* تنسيق الطباعة */
@media print {
    .receipts-btn,
    .receipts-form-group {
        display: none !important;
    }
    
    .receipts-table {
        border: 2px solid #000;
    }
    
    .receipts-table th,
    .receipts-table td {
        border: 1px solid #000;
        padding: 5px;
    }
    
    .receipts-table th {
        background-color: #f0f0f0 !important;
        -webkit-print-color-adjust: exact;
    }
}

/* تنسيق التركيز والتفاعل */
.receipts-patrol-editable-cell:hover,
.receipts-shifts-editable-cell:hover {
    background-color: var(--bs-secondary-bg);
}

.receipts-patrol-location-select:hover,
.receipts-shifts-location-select:hover {
    background-color: var(--bs-secondary-bg);
}

/* تنسيق الحالات الخاصة */
.receipts-table .empty-cell {
    background-color: var(--bs-light);
    color: var(--bs-secondary);
    font-style: italic;
}

.receipts-table .required-cell {
    background-color: var(--bs-warning-bg-subtle);
}

.receipts-table .completed-cell {
    background-color: var(--bs-success-bg-subtle);
}

/* تنسيق الرسوم المتحركة */
.receipts-fade-in {
    animation: receiptsFadeIn 0.3s ease-in;
}

@keyframes receiptsFadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.receipts-slide-in {
    animation: receiptsSlideIn 0.4s ease-out;
}

@keyframes receiptsSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
