#!/usr/bin/env python
# -*- coding: utf-8 -*-

print("🔍 اختبار الاستيراد...")

try:
    print("1. اختبار استيراد Flask...")
    from flask import Flask
    print("✅ Flask تم استيراده بنجاح")
    
    print("2. اختبار استيراد models...")
    from models import db
    print("✅ models.db تم استيراده بنجاح")
    
    print("3. اختبار استيراد النماذج الجديدة...")
    from models import ReceiptData, ReceiptPatrolData, ReceiptShiftsData
    print("✅ النماذج الجديدة تم استيرادها بنجاح")
    
    print("4. اختبار استيراد receipts_isolated...")
    from receipts_isolated import receipts_isolated_bp
    print("✅ receipts_isolated تم استيراده بنجاح")
    
    print("5. اختبار إنشاء التطبيق...")
    from app import create_app
    app = create_app()
    print("✅ التطبيق تم إنشاؤه بنجاح")
    
    print("6. اختبار إنشاء الجداول...")
    with app.app_context():
        db.create_all()
        print("✅ الجداول تم إنشاؤها بنجاح")
    
    print("🎉 جميع الاختبارات نجحت!")
    
except Exception as e:
    print(f"❌ خطأ في الاختبار: {e}")
    import traceback
    traceback.print_exc()
