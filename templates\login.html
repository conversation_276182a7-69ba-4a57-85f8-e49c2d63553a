{% extends "base.html" %}

{% block auth_content %}
{% block styles %}
<link href="/static/css/tailwind.min.css" rel="stylesheet">
<link rel="stylesheet" href="/static/css/fontawesome.min.css">
<style>
	body {
		font-family: 'Ta<PERSON><PERSON>', sans-serif;
		background-color: #1a1a1a;
		color: #ffffff;
	}

	.main-container {
		background-color: #1f1f1f;
		border-radius: 8px;
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
	}

	body.light-theme .main-container {
		background-color: #f0f0f0;
		color: #000000;
	}

	body.light-theme .text-gray-300 {
		--tw-text-opacity: 1;
		color: rgba(107, 114, 128, var(--tw-text-opacity));
	}

	body.light-theme .form-input {
		background-color: #ededed;
		border: 1px solid #d3d3d3;
		color: #3d3d3d;
	}

	body.light-theme .bg-gray-800 {
		--tw-bg-opacity: 1;
		background-color: rgba(229, 231, 235, var(--tw-bg-opacity));
	}

	body.light-theme .custom-checkbox {
		background-color: #dadada;
		border: 1px solid #d3d3d3;
	}

	.btn-primary {
		background-color: #2563eb;
		color: white;
		transition: all 0.3s;
	}

	.btn-primary:hover {
		background-color: #1d4ed8;
		transform: translateY(-2px);
	}

	.icon-box {
		width: 50px;
		height: 50px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 8px;
	}

	.red-icon {
		background-color: rgba(239, 68, 68, 0.2);
		color: #ef4444;
	}

	.blue-icon {
		background-color: rgba(59, 130, 246, 0.2);
		color: #3b82f6;
	}

	.green-icon {
		background-color: rgba(34, 197, 94, 0.2);
		color: #22c55e;
	}

	.form-input {
		background-color: #2d2d2d;
		border: 1px solid #3d3d3d;
		color: white;
		border-radius: 6px;
		padding: 10px 12px;
		width: 100%;
	}

	.form-input:focus {
		border-color: #3b82f6;
		outline: none;
	}

	/* Custom checkbox styling */
	.custom-checkbox {
		width: 18px;
		height: 18px;
		background-color: #2d2d2d;
		border: 1px solid #3d3d3d;
		border-radius: 4px;
		position: relative;
		cursor: pointer;
	}

	.custom-checkbox::after {
		content: '';
		position: absolute;
		display: none;
		left: 6px;
		top: 2px;
		width: 5px;
		height: 10px;
		border: solid white;
		border-width: 0 2px 2px 0;
		transform: rotate(45deg);
	}

	input[type="checkbox"]:checked+.custom-checkbox {
		background-color: #3b82f6;
		border-color: #3b82f6;
	}

	input[type="checkbox"]:checked+.custom-checkbox::after {
		display: block;
	}

	/* Logo animation */
	.logo-pulse {
		animation: pulse 2s infinite;
	}

	@keyframes pulse {
		0% {
			transform: scale(1);
		}

		50% {
			transform: scale(1.05);
		}

		100% {
			transform: scale(1);
		}
	}
</style>
{% endblock %}
<div class="min-h-screen flex items-center justify-center p-4">
	<div class="main-container w-full max-w-4xl p-8">
		<div class="flex flex-col md:flex-row gap-8 items-center">
			<!-- Left side - System info -->
			<div class="w-full md:w-1/2 flex flex-col items-center md:items-start">
				<div class="logo-pulse mx-auto mb-6">
					<img src="{{ url_for('static', filename='images/KSA.png') }}" alt="شعار المملكة" class="img-fluid"
						style="max-width: 100px;">
					<!-- <svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 24 24" fill="none"
						stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-500">
						<rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
						<rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
						<line x1="6" y1="6" x2="6.01" y2="6"></line>
						<line x1="6" y1="18" x2="6.01" y2="18"></line>
					</svg> -->
				</div>

				<h1 class="text-3xl font-bold mb-4 text-center w-full">نظام عتاد</h1>

				<p class="text-gray-300 mb-6 text-center md:text-right w-full">
					نظام متكامل لإدارة المستودعات والمخزون بكفاءة عالية. يوفر النظام لوحة تحكم متطورة لمراقبة حركة
					المخزون وإدارة المستودعات المتعددة.
				</p>

				<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8 w-full">
					<div class="flex items-center gap-3">
						<div class="icon-box red-icon">
							<i class="fas fa-box"></i>
						</div>
						<span>إدارة المخزون</span>
					</div>
					<div class="flex items-center gap-3">
						<div class="icon-box blue-icon">
							<i class="fas fa-chart-line"></i>
						</div>
						<span>متابعة الأنشطة</span>
					</div>
					<div class="flex items-center gap-3">
						<div class="icon-box green-icon">
							<i class="fas fa-file-alt"></i>
						</div>
						<span>تقارير متكاملة</span>
					</div>
				</div>

				<div class="rounded-lg bg-gray-800 p-4 w-full mb-6">
					<h3 class="text-xl font-bold mb-3 text-blue-400">لوحة تحكم متطورة</h3>
					<p class="text-gray-300">
						توفر لوحة التحكم إحصائيات دقيقة وعرض مرئي للبيانات لمساعدتك في اتخاذ القرارات المناسبة بسرعة
						وكفاءة.
					</p>
				</div>
			</div>

			<!-- Right side - Login form -->
			<div class="w-full md:w-1/2">
				<div class="bg-gray-800 rounded-lg p-6 shadow-lg">
					<h2 class="text-2xl font-bold mb-6 text-center">تسجيل الدخول</h2>

					<form method="POST" action="{{ url_for('auth.login') }}" class="space-y-5" id="loginForm">
						{{ form.csrf_token }}
						<script>
							// التحكم في ملء البيانات المخزنة تلقائيًا
							window.onload = function() {
								// التحقق من حالة خيار "تذكرني" المخزنة
								const rememberMeEnabled = localStorage.getItem('rememberMe') === 'true';

								// إذا لم يكن خيار "تذكرني" مفعلاً، امسح البيانات
								if (!rememberMeEnabled) {
									// تأخير قصير للتأكد من أن المتصفح قد أكمل محاولة الملء التلقائي
									setTimeout(function() {
										document.getElementById('username').value = '';
										document.getElementById('password').value = '';
									}, 100);
								}

								// إضافة مستمع حدث لخيار "تذكرني"
								const rememberMeCheckbox = document.getElementById('remember-me');
								const loginForm = document.getElementById('loginForm');

								// تعيين حالة النموذج بناءً على حالة "تذكرني" المخزنة
								if (rememberMeEnabled) {
									rememberMeCheckbox.checked = true;
									loginForm.setAttribute('autocomplete', 'on');
								} else {
									loginForm.setAttribute('autocomplete', 'off');
								}

								// تحديث الإعدادات عند تغيير حالة "تذكرني"
								rememberMeCheckbox.addEventListener('change', function() {
									localStorage.setItem('rememberMe', this.checked);

									if (this.checked) {
										loginForm.setAttribute('autocomplete', 'on');
									} else {
										loginForm.setAttribute('autocomplete', 'off');
										// مسح البيانات المخزنة عند إلغاء تفعيل "تذكرني"
										document.getElementById('username').value = '';
										document.getElementById('password').value = '';
									}
								});
							}
						</script>
						<div>
							<label for="username" class="block mb-2 text-gray-300">{{ form.username.label }}</label>
							<input type="text" name="{{ form.username.name }}" id="username" class="form-input" required placeholder="أدخل اسم المستخدم" readonly onfocus="this.removeAttribute('readonly');">
						</div>

						<div>
							<label for="password" class="block mb-2 text-gray-300">{{ form.password.label }}</label>
							<input type="password" name="{{ form.password.name }}" id="password" class="form-input" required placeholder="أدخل كلمة المرور" readonly onfocus="this.removeAttribute('readonly');">
						</div>

						<div class="flex items-center justify-between">
							<div class="flex items-center gap-2">
								{{ form.remember_me(class="sr-only", id="remember-me") }}
								<div class="custom-checkbox"></div>
								<label for="remember-me" class="text-gray-300 cursor-pointer">{{ form.remember_me.label }}</label>
							</div>
						</div>

						<button type="submit"
							class="btn-primary py-3 px-6 rounded-md w-full font-bold flex items-center justify-center gap-2">
							<i class="fas fa-sign-in-alt"></i>
							تسجيل الدخول
						</button>
					</form>

					<div class="mt-6 pt-6 border-t border-gray-700">
						<div class="flex items-center justify-center gap-2 text-sm text-gray-400">
							<i class="fas fa-shield-alt"></i>
							<span>تسجيل الدخول آمن ومشفر</span>
						</div>
					</div>
				</div>

				<div class="mt-6 bg-gray-800 rounded-lg p-4">
					<h3 class="font-bold mb-2 text-center">الدعم الفني</h3>
					<div class="flex justify-center gap-4">
						<a href="mailto:<EMAIL>" target="_blank" class="text-gray-400 hover:text-blue-400">
							<i class="fas fa-headset text-xl"></i>
						</a>
						<a href="mailto:<EMAIL>" target="_blank" class="text-gray-400 hover:text-blue-400">
							<i class="fas fa-envelope text-xl"></i>
						</a>
						<a href="mailto:<EMAIL>" target="_blank" class="text-gray-400 hover:text-blue-400">
							<i class="fas fa-question-circle text-xl"></i>
						</a>
					</div>
				</div>
			</div>
		</div>

		<footer class="mt-10 text-center text-gray-500 text-sm">
			<p>&copy; جميع الحقوق محفوظة لدى سرية الأمن والحماية الأولى بالكتيبة الخاصة الرابعة 2025</p>
		</footer>
	</div>
</div>


<!-- زر تبديل المظهر النهاري/الليلي -->
<button id="theme-switcher" class="btn btn-link position-fixed" style="top: 15px; left: 15px; z-index: 1000;">
	<i class="fas fa-sun"></i>
</button>

<!-- إضافة سكريبت خاص بتفعيل زر تبديل المظهر في صفحة تسجيل الدخول -->
<script>
	document.addEventListener('DOMContentLoaded', function () {
		const themeSwitcher = document.getElementById('theme-switcher');

		if (themeSwitcher) {

			themeSwitcher.addEventListener('click', function () {

				const icon = this.querySelector('i');

				if (document.body.classList.contains('light-theme')) {
					icon.classList.remove('fa-sun');
					icon.classList.add('fa-moon');
					localStorage.setItem('theme', 'light');
				} else {
					icon.classList.remove('fa-moon');
					icon.classList.add('fa-sun');
					localStorage.setItem('theme', 'dark');
				}
			});

			// التحقق من السمة المخزنة
			const savedTheme = localStorage.getItem('theme');
			if (savedTheme === 'light') {
				document.body.classList.add('light-theme');
				const icon = themeSwitcher.querySelector('i');
				icon.classList.remove('fa-sun');
				icon.classList.add('fa-moon');
			}
		}
	});
</script>
{% endblock %}

{% block title %}تسجيل الدخول - نظام عتاد{% endblock %}