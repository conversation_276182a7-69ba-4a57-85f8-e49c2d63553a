{% extends "base.html" %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>
            المستودعات
        </h3>
    </div>
    <div class="col-md-4 text-right">
        {% if current_user.role == 'admin' %}
        <a href="{{ url_for('warehouse.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة مستودع جديد
        </a>
        {% endif %}
    </div>
</div>

<div class="row">
    {% for warehouse in warehouses %}
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ warehouse.name }}</h5>
            </div>
            <div class="card-body">
                <div class="warehouse-stats">
                    <div class="stat-item">
                        <div class="icon">
                            <i class="fas fa-crosshairs"></i>
                        </div>
                        <div class="stat">
                            <div class="label">الأسلحة</div>
                            <div class="value">{{ warehouse.weapons.count() }}</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat">
                            <div class="label">الأفراد</div>
                            <div class="value">{{ warehouse.personnel.count() }}</div>
                        </div>
                    </div>

                </div>
                <div class="warehouse-location mt-3">
                    <i class="fas fa-map-marker-alt text-danger mr-2"></i> {{ warehouse.location or 'لا يوجد موقع محدد'
                    }}
                </div>
                <div class="warehouse-description mt-2">
                    {{ warehouse.description or 'لا يوجد وصف' }}
                </div>
            </div>
            <div class="card-footer">
                <div class="btn-block d-flex justify-content-between">
                    <a href="{{ url_for('weapons.warehouse_weapons', warehouse_id=warehouse.id) }}"
                        class="btn btn-outline-primary">
                        <i class="fas fa-crosshairs"></i> الأسلحة
                    </a>
                    <a href="{{ url_for('personnel.warehouse_personnel', warehouse_id=warehouse.id) }}"
                        class="btn btn-outline-info">
                        <i class="fas fa-users"></i> الأفراد
                    </a>
                </div>
            </div>
            {% if warehouse.name in ['المستودع الأول', 'المستودع الثاني', 'المخزون العام'] %}
            <div class="card-footer">
                <a href="{{ url_for('warehouse.display', warehouse_id=warehouse.id) }}"
                    class="btn btn-block btn-outline-dark w-100">
                    <i class="fas fa-tv"></i> عرض شاشة المستودع
                </a>
            </div>
            {% endif %}
        </div>
    </div>
    {% else %}
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> لا توجد مستودعات مسجلة حتى الآن
        </div>
    </div>
    {% endfor %}
</div>

{% if warehouses %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">توزيع الأسلحة بين المستودعات</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 250px;">
                    <canvas id="weaponsDistributionChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">توزيع الأفراد بين المستودعات</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 250px;">
                    <canvas id="personnelDistributionChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
{% if warehouses %}
<script src="{{ url_for('static', filename='js/Chart.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Ensure Chart.js is loaded and accessible globally
        if (typeof Chart === 'undefined') {
            console.error('Chart.js is not loaded correctly. Please check the file path or dependencies.');
            return;
        }

        // توزيع الأسلحة بين المستودعات
        const warehouseNames = [];
        const weaponCounts = [];
        const personnelCounts = [];

        {% for warehouse in warehouses %}
        warehouseNames.push('{{ warehouse.name }}');
        weaponCounts.push({{ warehouse.weapons.count() }});
        personnelCounts.push({{ warehouse.personnel.count() }});
        {% endfor %}

        // رسم بياني لتوزيع الأسلحة
        const weaponsCtx = document.getElementById('weaponsDistributionChart').getContext('2d');
        new Chart(weaponsCtx, {
            type: 'bar',
            data: {
                labels: warehouseNames,
                datasets: [{
                    label: 'عدد الأسلحة',
                    data: weaponCounts,
                    backgroundColor: '#007bff',
                    borderColor: '#0056b3',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function (value) {
                                if (value % 1 === 0) {
                                    return value;
                                }
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // رسم بياني لتوزيع الأفراد
        const personnelCtx = document.getElementById('personnelDistributionChart').getContext('2d');
        new Chart(personnelCtx, {
            type: 'bar',
            data: {
                labels: warehouseNames,
                datasets: [{
                    label: 'عدد الأفراد',
                    data: personnelCounts,
                    backgroundColor: '#28a745',
                    borderColor: '#1e7e34',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function (value) {
                                if (value % 1 === 0) {
                                    return value;
                                }
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    });
</script>
{% endif %}
{% endblock %}