#!/usr/bin/env python3
"""
سكريبت إنشاء جداول كشف الواجبات في PostgreSQL
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

def create_duty_tables():
    """إنشاء جداول كشف الواجبات في PostgreSQL"""
    
    # إعدادات قاعدة البيانات
    DATABASE_URL = os.environ.get("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/military_warehouse")
    
    try:
        # إنشاء الاتصال
        engine = create_engine(DATABASE_URL)
        
        print("🔗 الاتصال بقاعدة البيانات PostgreSQL...")
        
        with engine.connect() as conn:
            # بدء المعاملة
            trans = conn.begin()
            
            try:
                print("📋 إنشاء جدول duty_data...")
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS duty_data (
                        id SERIAL PRIMARY KEY,
                        location_id INTEGER NOT NULL,
                        user_id INTEGER NOT NULL DEFAULT 1,
                        duty_date DATE NOT NULL,
                        duty_time TIME NOT NULL,
                        duty_data TEXT NOT NULL,
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE CASCADE,
                        FOREIGN KEY (user_id) REFERENCES users(id)
                    )
                """))
                
                print("📋 إنشاء جدول duty_personnel...")
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS duty_personnel (
                        id SERIAL PRIMARY KEY,
                        duty_data_id INTEGER NOT NULL,
                        personnel_id INTEGER NOT NULL,
                        duty_position VARCHAR(100),
                        duty_status VARCHAR(50) DEFAULT 'حاضر',
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (duty_data_id) REFERENCES duty_data(id) ON DELETE CASCADE,
                        FOREIGN KEY (personnel_id) REFERENCES personnel(id) ON DELETE CASCADE
                    )
                """))
                
                print("📋 إنشاء جدول duty_templates...")
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS duty_templates (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(200) NOT NULL,
                        location_id INTEGER NOT NULL,
                        template_data TEXT NOT NULL,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_by INTEGER DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE CASCADE,
                        FOREIGN KEY (created_by) REFERENCES users(id)
                    )
                """))
                
                print("🔍 إنشاء الفهارس...")
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_duty_data_location 
                    ON duty_data(location_id)
                """))
                
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_duty_data_date 
                    ON duty_data(duty_date)
                """))
                
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_duty_personnel_duty 
                    ON duty_personnel(duty_data_id)
                """))
                
                conn.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_duty_templates_location 
                    ON duty_templates(location_id)
                """))
                
                # تأكيد المعاملة
                trans.commit()
                print("✅ تم إنشاء جميع جداول كشف الواجبات والفهارس بنجاح!")
                
                # التحقق من الجداول المنشأة
                print("\n🔍 التحقق من الجداول المنشأة:")
                result = conn.execute(text("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name IN ('duty_data', 'duty_personnel', 'duty_templates')
                    ORDER BY table_name
                """))
                
                tables = result.fetchall()
                for table in tables:
                    print(f"   ✓ {table[0]}")
                
                return True
                
            except Exception as e:
                trans.rollback()
                print(f"❌ خطأ في إنشاء الجداول: {str(e)}")
                return False
                
    except SQLAlchemyError as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ خطأ عام: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إنشاء جداول كشف الواجبات في PostgreSQL")
    print("="*60)
    
    success = create_duty_tables()
    
    if success:
        print("\n🎉 تم إنشاء جميع جداول كشف الواجبات بنجاح!")
        print("📝 الجداول الجديدة:")
        print("   - duty_data: بيانات كشف الواجبات")
        print("   - duty_personnel: أفراد الواجبات")
        print("   - duty_templates: قوالب الواجبات")
        return 0
    else:
        print("\n❌ فشل في إنشاء الجداول!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
