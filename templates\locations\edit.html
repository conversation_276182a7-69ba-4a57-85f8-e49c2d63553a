{% extends "base.html" %}

{% block title %}تعديل الموقع - {{ location.name }}{% endblock %}

{% block styles %}
<style>
    .form-container {
        background: var(--card-bg);
        border-radius: 12px;
        padding: 30px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        border: 1px solid var(--border-color);
    }

    .section-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        font-weight: 600;
    }

    .form-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        background: var(--section-bg);
    }

    .equipment-item {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        position: relative;
    }

    .equipment-item .remove-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .equipment-item .remove-btn:hover {
        background: #c82333;
        transform: scale(1.1);
    }

    .add-equipment-btn {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .add-equipment-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }

    .form-actions {
        background: var(--section-bg);
        padding: 20px;
        border-radius: 8px;
        border: 1px solid var(--border-color);
        display: flex;
        gap: 15px;
        justify-content: flex-end;
    }

    .btn-primary {
        background: linear-gradient(135deg, #007bff, #0056b3);
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 12px 24px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-2px);
    }

    .form-label {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 8px;
    }

    .form-control, .form-select {
        border: 1px solid var(--border-color);
        border-radius: 6px;
        padding: 10px 12px;
        transition: all 0.2s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .required {
        color: #dc3545;
    }

    .personnel-item {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
    }

    .personnel-result {
        transition: all 0.2s ease;
    }

    .personnel-result:hover {
        background-color: var(--hover-bg) !important;
    }

    @media (max-width: 768px) {
        .form-container {
            padding: 20px;
        }
        
        .form-actions {
            flex-direction: column;
        }
        
        .form-actions .btn {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="mb-2">تعديل الموقع</h1>
            <p class="text-muted mb-0">تعديل بيانات الموقع والعهد المخصصة له</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ url_for('locations.view_location', location_id=location.id) }}" class="btn btn-outline-info">
                <i class="fas fa-eye"></i>
                عرض الموقع
            </a>
            <a href="{{ url_for('locations.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <div class="form-container">
        <form id="editLocationForm" method="POST" action="{{ url_for('locations.update_location_api', location_id=location.id) }}" enctype="multipart/form-data">
            <!-- Basic Information -->
            <div class="form-section">
                <div class="section-header">
                    <i class="fas fa-info-circle"></i>
                    المعلومات الأساسية
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم الموقع <span class="required">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ location.name }}" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="serial_number" class="form-label">الرقم التسلسلي <span class="required">*</span></label>
                            <input type="text" class="form-control" id="serial_number" name="serial_number" value="{{ location.serial_number }}" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="type" class="form-label">نوع الموقع</label>
                            <select class="form-select" id="type" name="type">
                                <option value="أمني" {{ 'selected' if location.type == 'أمني' else '' }}>أمني</option>
                                <option value="إداري" {{ 'selected' if location.type == 'إداري' else '' }}>إداري</option>
                                <option value="خدمي" {{ 'selected' if location.type == 'خدمي' else '' }}>خدمي</option>
                                <option value="تقني" {{ 'selected' if location.type == 'تقني' else '' }}>تقني</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="status" class="form-label">حالة الموقع</label>
                            <select class="form-select" id="status" name="status">
                                <option value="نشط" {{ 'selected' if location.status == 'نشط' else '' }}>نشط</option>
                                <option value="غير نشط" {{ 'selected' if location.status == 'غير نشط' else '' }}>غير نشط</option>
                                <option value="مسحوب" {{ 'selected' if location.status == 'مسحوب' else '' }}>مسحوب</option>
                                <option value="تحت الصيانة" {{ 'selected' if location.status == 'تحت الصيانة' else '' }}>تحت الصيانة</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="coordinates" class="form-label">الإحداثيات</label>
                            <input type="text" class="form-control" id="coordinates" name="coordinates" value="{{ location.coordinates or '' }}" placeholder="مثال: 24.7136/46.6753">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="description" class="form-label">وصف الموقع</label>
                    <textarea class="form-control" id="description" name="description" rows="3" placeholder="أدخل وصف مفصل للموقع...">{{ location.description or '' }}</textarea>
                </div>

                <!-- Instructions File Section -->
                <div class="mb-4">
                    <label class="form-label">ملف تعليمات الموقع (PDF)</label>

                    {% if location.instructions_file %}
                    <!-- Current File Display -->
                    <div class="current-file-info mb-3 p-3 border rounded" style="background-color: #f8f9fa;">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-file-pdf text-danger"></i>
                                <span class="ms-2">ملف التعليمات الحالي</span>
                                <small class="text-muted d-block">{{ location.instructions_file }}</small>
                            </div>
                            <div>
                                <a href="{{ url_for('locations.download_instructions', location_id=location.id) }}"
                                   class="btn btn-sm btn-outline-primary me-2">
                                    <i class="fas fa-download"></i> تحميل
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger"
                                        onclick="removeInstructionsFile()">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- File Upload -->
                    <div class="file-upload-section">
                        <input type="file" class="form-control" id="instructions_file" name="instructions_file" accept=".pdf">
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            {% if location.instructions_file %}
                                يمكنك رفع ملف PDF جديد لاستبدال الملف الحالي
                            {% else %}
                                يمكنك رفع ملف PDF يحتوي على تعليمات وإرشادات خاصة بهذا الموقع
                            {% endif %}
                        </div>
                    </div>

                    <!-- Hidden field to track file removal -->
                    <input type="hidden" id="remove_instructions_file" name="remove_instructions_file" value="false">
                </div>
            </div>

            <!-- Equipment Section -->
            <div class="form-section">
                <div class="section-header">
                    <i class="fas fa-boxes"></i>
                    العهد المخصصة للموقع
                </div>
                
                <div id="equipmentContainer">
                    {% if location.equipment %}
                        {% for equipment in location.equipment %}
                        <div class="equipment-item" id="equipment-{{ loop.index }}">
                            <button type="button" class="remove-btn" onclick="removeEquipmentItem({{ loop.index }})">
                                <i class="fas fa-times"></i>
                            </button>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">اسم العهدة</label>
                                        <input type="text" class="form-control" name="equipment[{{ loop.index }}][name]" value="{{ equipment.equipment_name }}" placeholder="مثال: جهاز لاسلكي">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">نوع العهدة</label>
                                        <select class="form-select" name="equipment[{{ loop.index }}][type]">
                                            <option value="">اختر النوع</option>
                                            <option value="جهاز لاسلكي" {{ 'selected' if equipment.equipment_type == 'جهاز لاسلكي' else '' }}>جهاز لاسلكي</option>
                                            <option value="قارئ بطاقات" {{ 'selected' if equipment.equipment_type == 'قارئ بطاقات' else '' }}>قارئ بطاقات</option>
                                            <option value="كاميرا مراقبة" {{ 'selected' if equipment.equipment_type == 'كاميرا مراقبة' else '' }}>كاميرا مراقبة</option>
                                            <option value="جهاز إنذار" {{ 'selected' if equipment.equipment_type == 'جهاز إنذار' else '' }}>جهاز إنذار</option>
                                            <option value="معدات أمنية" {{ 'selected' if equipment.equipment_type == 'معدات أمنية' else '' }}>معدات أمنية</option>
                                            <option value="أخرى" {{ 'selected' if equipment.equipment_type == 'أخرى' else '' }}>أخرى</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="mb-3">
                                        <label class="form-label">الكمية</label>
                                        <input type="number" class="form-control" name="equipment[{{ loop.index }}][quantity]" value="{{ equipment.quantity }}" min="1">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="mb-3">
                                        <label class="form-label">الحالة</label>
                                        <select class="form-select" name="equipment[{{ loop.index }}][condition]">
                                            <option value="جيد" {{ 'selected' if equipment.condition_status == 'جيد' else '' }}>جيد</option>
                                            <option value="متوسط" {{ 'selected' if equipment.condition_status == 'متوسط' else '' }}>متوسط</option>
                                            <option value="يحتاج صيانة" {{ 'selected' if equipment.condition_status == 'يحتاج صيانة' else '' }}>يحتاج صيانة</option>
                                            <option value="معطل" {{ 'selected' if equipment.condition_status == 'معطل' else '' }}>معطل</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الرقم التسلسلي</label>
                                        <input type="text" class="form-control" name="equipment[{{ loop.index }}][serial_number]" value="{{ equipment.serial_number or '' }}" placeholder="الرقم التسلسلي للعهدة">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">ملاحظات</label>
                                        <input type="text" class="form-control" name="equipment[{{ loop.index }}][notes]" value="{{ equipment.notes or '' }}" placeholder="ملاحظات إضافية">
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% endif %}
                </div>
                
                <button type="button" class="add-equipment-btn" onclick="addEquipmentItem()">
                    <i class="fas fa-plus"></i>
                    إضافة عهدة جديدة
                </button>
            </div>

            <!-- Personnel Section -->
            <div class="form-section">
                <div class="section-header">
                    <i class="fas fa-users"></i>
                    الأفراد المرتبطين بالموقع
                </div>

                <div id="personnelContainer">
                    {% if location.personnel %}
                        {% for person in location.personnel %}
                        <div class="personnel-item" id="personnel-{{ person.id }}">
                            <div class="row align-items-center">
                                <div class="col-md-3">
                                    <strong>{{ person.name }}</strong><br>
                                    <small class="text-muted">{{ person.personnel_id }}</small>
                                </div>
                                <div class="col-md-2">
                                    <span class="badge bg-primary">{{ person.rank }}</span>
                                </div>
                                <div class="col-md-2">
                                    {% if person.status == 'نشط' %}
                                    <span class="badge badge-success">{{ person.status }}</span>
                                    {% elif person.status == 'إجازة' %}
                                    <span class="badge badge-warning">{{ person.status }}</span>
                                    {% elif person.status == 'مهمة' %}
                                    <span class="badge badge-mission">{{ person.status }}</span>
                                    {% elif person.status == 'دورة' %}
                                    <span class="badge badge-danger">{{ person.status }}</span>
                                    {% elif person.status == 'مستلم' %}
                                    <span class="badge badge-primary">{{ person.status }}</span>
                                    {% elif person.status == 'رماية' %}
                                    <span class="badge badge-shooting">{{ person.status }}</span>
                                    {% else %}
                                    <span class="badge badge-secondary">{{ person.status }}</span>
                                    {% endif %}
                                </div>
                                <div class="col-md-2">
                                    <span class="badge bg-info">{{ person.shift_type }}</span>
                                </div>
                                <div class="col-md-2">
                                    <small class="text-muted">{{ person.notes or 'لا توجد ملاحظات' }}</small>
                                </div>
                                <div class="col-md-1">
                                    <button type="button" class="btn btn-sm btn-danger" onclick="removePersonnel({{ person.id }})">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted py-4" id="noPersonnelMessage">
                            <i class="fas fa-users fa-3x mb-3"></i>
                            <p>لا يوجد أفراد مرتبطين بهذا الموقع</p>
                        </div>
                    {% endif %}
                </div>

                <div class="mt-3">
                    <button type="button" class="add-equipment-btn" onclick="showAddPersonnelModal()">
                        <i class="fas fa-user-plus"></i>
                        إضافة فرد للموقع
                    </button>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    حفظ التغييرات
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Add Personnel Modal -->
<div class="modal fade" id="addPersonnelModal" tabindex="-1" aria-labelledby="addPersonnelModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addPersonnelModalLabel">إضافة فرد للموقع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addPersonnelForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="searchTerm" class="form-label">رقم الهوية الوطنية أو الرقم العسكري</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchTerm"
                                           placeholder="أدخل رقم الهوية أو الرقم العسكري"
                                           oninput="autoSearchPersonnel(this.value)">
                                    <button type="button" class="btn btn-outline-primary" onclick="searchPersonnel()">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                </div>
                                <small class="form-text text-muted">سيتم البحث تلقائياً عند الكتابة</small>
                            </div>

                            <!-- Personnel Assignment Details -->
                            <div id="assignmentDetails" style="display: none;">
                                <div class="mb-3">
                                    <label for="shiftType" class="form-label">نوع الوردية</label>
                                    <select class="form-select" id="shiftType">
                                        <option value="صباحية">صباحية</option>
                                        <option value="مسائية">مسائية</option>
                                        <option value="ليلية">ليلية</option>
                                        <option value="عام" selected>عام</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="personnelNotes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="personnelNotes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <!-- Personnel Information Display -->
                            <div id="personnelInfo" style="display: none;">
                                <div class="card personnel-info-card search-result-enter">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-user"></i> معلومات الفرد</h6>
                                    </div>
                                    <div class="card-body" id="personnelDetails">
                                        <!-- سيتم ملء هذا القسم ديناميكياً -->
                                    </div>
                                </div>
                            </div>

                            <!-- Search Results for Multiple Results -->
                            <div id="searchResults" style="display: none;">
                                <div class="card search-result-enter">
                                    <div class="card-header">
                                        <h6 class="mb-0"><i class="fas fa-search"></i> نتائج البحث</h6>
                                    </div>
                                    <div class="card-body search-results-container" id="personnelResults" style="max-height: 300px; overflow-y: auto;">
                                        <!-- سيتم ملء هذا القسم ديناميكياً -->
                                    </div>
                                </div>
                            </div>

                            <!-- Search Status -->
                            <div id="searchStatus" style="display: none;">
                                <div class="alert alert-info search-status">
                                    <i class="fas fa-spinner fa-spin spinner"></i> جاري البحث...
                                </div>
                            </div>

                            <!-- No Results -->
                            <div id="noResults" style="display: none;">
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i> لم يتم العثور على أفراد بهذا الرقم
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="addPersonnelBtn" onclick="addPersonnelToLocation()" style="display: none;">
                    <i class="fas fa-plus"></i> إضافة للموقع
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let equipmentCounter = {{ location.equipment.count() if location.equipment else 0 }};
let selectedPersonnel = null;

function addEquipmentItem() {
    equipmentCounter++;
    const container = document.getElementById('equipmentContainer');
    
    const equipmentItem = document.createElement('div');
    equipmentItem.className = 'equipment-item';
    equipmentItem.id = `equipment-${equipmentCounter}`;
    
    equipmentItem.innerHTML = `
        <button type="button" class="remove-btn" onclick="removeEquipmentItem(${equipmentCounter})">
            <i class="fas fa-times"></i>
        </button>
        
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label class="form-label">اسم العهدة</label>
                    <input type="text" class="form-control" name="equipment[${equipmentCounter}][name]" placeholder="مثال: جهاز لاسلكي">
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">نوع العهدة</label>
                    <select class="form-select" name="equipment[${equipmentCounter}][type]">
                        <option value="">اختر النوع</option>
                        <option value="جهاز لاسلكي">جهاز لاسلكي</option>
                        <option value="قارئ بطاقات">قارئ بطاقات</option>
                        <option value="كاميرا مراقبة">كاميرا مراقبة</option>
                        <option value="جهاز إنذار">جهاز إنذار</option>
                        <option value="معدات أمنية">معدات أمنية</option>
                        <option value="أخرى">أخرى</option>
                    </select>
                </div>
            </div>
            <div class="col-md-2">
                <div class="mb-3">
                    <label class="form-label">الكمية</label>
                    <input type="number" class="form-control" name="equipment[${equipmentCounter}][quantity]" value="1" min="1">
                </div>
            </div>
            <div class="col-md-3">
                <div class="mb-3">
                    <label class="form-label">الحالة</label>
                    <select class="form-select" name="equipment[${equipmentCounter}][condition]">
                        <option value="جيد">جيد</option>
                        <option value="متوسط">متوسط</option>
                        <option value="يحتاج صيانة">يحتاج صيانة</option>
                        <option value="معطل">معطل</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">الرقم التسلسلي</label>
                    <input type="text" class="form-control" name="equipment[${equipmentCounter}][serial_number]" placeholder="الرقم التسلسلي للعهدة">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">ملاحظات</label>
                    <input type="text" class="form-control" name="equipment[${equipmentCounter}][notes]" placeholder="ملاحظات إضافية">
                </div>
            </div>
        </div>
    `;
    
    container.appendChild(equipmentItem);
}

function removeEquipmentItem(id) {
    const item = document.getElementById(`equipment-${id}`);
    if (item) {
        item.remove();
    }
}

// Personnel Management Functions
let searchTimeout = null;

function showAddPersonnelModal() {
    const modal = new bootstrap.Modal(document.getElementById('addPersonnelModal'));
    modal.show();

    // Reset form
    document.getElementById('searchTerm').value = '';
    hideAllSearchElements();
    selectedPersonnel = null;
}

function hideAllSearchElements() {
    document.getElementById('searchResults').style.display = 'none';
    document.getElementById('personnelInfo').style.display = 'none';
    document.getElementById('assignmentDetails').style.display = 'none';
    document.getElementById('searchStatus').style.display = 'none';
    document.getElementById('noResults').style.display = 'none';
    document.getElementById('addPersonnelBtn').style.display = 'none';
}

function autoSearchPersonnel(searchTerm) {
    // Clear previous timeout
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }

    // Hide all elements first
    hideAllSearchElements();

    // If search term is empty, return
    if (!searchTerm.trim()) {
        return;
    }

    // Show loading status
    document.getElementById('searchStatus').style.display = 'block';

    // Set timeout for auto search (500ms delay)
    searchTimeout = setTimeout(() => {
        performSearch(searchTerm.trim());
    }, 500);
}

function searchPersonnel() {
    const searchTerm = document.getElementById('searchTerm').value.trim();

    if (!searchTerm) {
        showNotification('يرجى إدخال رقم الهوية أو الرقم العسكري', 'warning');
        return;
    }

    hideAllSearchElements();
    document.getElementById('searchStatus').style.display = 'block';
    performSearch(searchTerm);
}

function performSearch(searchTerm) {
    fetch(`/locations/{{ location.id }}/personnel/search?search_term=${encodeURIComponent(searchTerm)}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('searchStatus').style.display = 'none';

            if (data.success && data.personnel && data.personnel.length > 0) {
                if (data.personnel.length === 1) {
                    // Single result - show personnel info directly
                    displaySinglePersonnelInfo(data.personnel[0]);
                } else {
                    // Multiple results - show selection list
                    displayPersonnelResults(data.personnel);
                }
            } else {
                document.getElementById('noResults').style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('searchStatus').style.display = 'none';
            showNotification('حدث خطأ أثناء البحث', 'error');
        });
}

function displaySinglePersonnelInfo(person) {
    selectedPersonnel = person;

    const statusBadgeClass = getStatusBadgeClass(person.status);

    document.getElementById('personnelDetails').innerHTML = `
        <div class="row">
            <div class="col-12">
                <h5 class="text-primary mb-3">${person.name}</h5>
            </div>
        </div>
        <div class="row mb-2">
            <div class="col-6"><strong>الرقم العسكري:</strong></div>
            <div class="col-6">${person.personnel_id}</div>
        </div>
        <div class="row mb-2">
            <div class="col-6"><strong>رقم الهوية:</strong></div>
            <div class="col-6">${person.phone || 'غير محدد'}</div>
        </div>
        <div class="row mb-2">
            <div class="col-6"><strong>الرتبة:</strong></div>
            <div class="col-6"><span class="badge bg-primary">${person.rank}</span></div>
        </div>
        <div class="row mb-2">
            <div class="col-6"><strong>الحالة:</strong></div>
            <div class="col-6"><span class="badge ${statusBadgeClass}">${person.status}</span></div>
        </div>
        <div class="row mb-2">
            <div class="col-6"><strong>المستودع:</strong></div>
            <div class="col-6">${person.warehouse_name}</div>
        </div>
        ${person.unit ? `
        <div class="row mb-2">
            <div class="col-6"><strong>الوحدة:</strong></div>
            <div class="col-6">${person.unit}</div>
        </div>
        ` : ''}
    `;

    document.getElementById('personnelInfo').style.display = 'block';
    document.getElementById('assignmentDetails').style.display = 'block';
    document.getElementById('addPersonnelBtn').style.display = 'block';
}

function displayPersonnelResults(personnel) {
    const resultsContainer = document.getElementById('personnelResults');
    resultsContainer.innerHTML = '';

    personnel.forEach(person => {
        const statusBadgeClass = getStatusBadgeClass(person.status);

        const personDiv = document.createElement('div');
        personDiv.className = 'border rounded p-3 mb-2 personnel-result';
        personDiv.style.cursor = 'pointer';
        personDiv.onclick = () => selectPersonnelFromList(person, personDiv);

        personDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${person.name}</strong><br>
                    <small class="text-muted">الرقم العسكري: ${person.personnel_id}</small><br>
                    <small class="text-muted">رقم الهوية: ${person.phone || 'غير محدد'}</small>
                </div>
                <div class="text-end">
                    <span class="badge bg-primary">${person.rank}</span><br>
                    <span class="badge ${statusBadgeClass}">${person.status}</span><br>
                    <small class="text-muted">${person.warehouse_name}</small>
                </div>
            </div>
        `;

        resultsContainer.appendChild(personDiv);
    });

    document.getElementById('searchResults').style.display = 'block';
}

function getStatusBadgeClass(status) {
    switch(status) {
        case 'نشط': return 'badge-success';
        case 'إجازة': return 'badge-warning';
        case 'مهمة': return 'badge-mission';
        case 'دورة': return 'badge-danger';
        case 'مستلم': return 'badge-primary';
        case 'رماية': return 'badge-shooting';
        default: return 'badge-secondary';
    }
}

function selectPersonnelFromList(person, element) {
    // Remove previous selection
    document.querySelectorAll('.personnel-result').forEach(el => {
        el.classList.remove('border-primary', 'bg-light');
    });

    // Highlight selected
    element.classList.add('border-primary', 'bg-light');

    // Hide search results and show personnel info
    document.getElementById('searchResults').style.display = 'none';
    displaySinglePersonnelInfo(person);
}

function addPersonnelToLocation() {
    if (!selectedPersonnel) {
        showNotification('يرجى اختيار فرد من نتائج البحث', 'warning');
        return;
    }

    const shiftType = document.getElementById('shiftType').value;
    const notes = document.getElementById('personnelNotes').value;

    const addBtn = document.getElementById('addPersonnelBtn');
    const originalText = addBtn.innerHTML;
    addBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإضافة...';
    addBtn.disabled = true;

    const data = {
        personnel_id: selectedPersonnel.id,
        shift_type: shiftType,
        notes: notes
    };

    fetch(`/locations/{{ location.id }}/personnel/add`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إضافة الفرد للموقع بنجاح', 'success');

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('addPersonnelModal'));
            modal.hide();

            // تحديث قائمة الأفراد فوراً بدلاً من إعادة تحميل الصفحة
            refreshPersonnelList();
        } else {
            // Show error message with better styling
            if (data.message.includes('مرتبط بالفعل بموقع:')) {
                showNotification(data.message, 'warning');
            } else if (data.message.includes('مضاف بالفعل في هذا الموقع')) {
                showNotification('هذا الفرد مضاف بالفعل في هذا الموقع', 'info');
            } else {
                showNotification(data.message || 'حدث خطأ أثناء إضافة الفرد', 'error');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('حدث خطأ أثناء إضافة الفرد', 'error');
    })
    .finally(() => {
        addBtn.innerHTML = originalText;
        addBtn.disabled = false;
    });
}

function removePersonnel(personnelId) {
    if (!confirm('هل أنت متأكد من إزالة هذا الفرد من الموقع؟')) {
        return;
    }

    fetch(`/locations/{{ location.id }}/personnel/${personnelId}/remove`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إزالة الفرد من الموقع بنجاح', 'success');

            // Remove personnel item from DOM
            const personnelItem = document.getElementById(`personnel-${personnelId}`);
            if (personnelItem) {
                personnelItem.remove();
            }

            // Check if no personnel left
            const personnelContainer = document.getElementById('personnelContainer');
            if (personnelContainer.children.length === 0) {
                personnelContainer.innerHTML = `
                    <div class="text-center text-muted py-4" id="noPersonnelMessage">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <p>لا يوجد أفراد مرتبطين بهذا الموقع</p>
                    </div>
                `;
            }
        } else {
            showNotification(data.message || 'حدث خطأ أثناء إزالة الفرد', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('حدث خطأ أثناء إزالة الفرد', 'error');
    });
}

// Form submission
document.getElementById('editLocationForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const equipment = [];
    
    // Collect equipment data
    const equipmentItems = document.querySelectorAll('.equipment-item');
    equipmentItems.forEach((item, index) => {
        const inputs = item.querySelectorAll('input, select');
        const equipmentData = {};
        
        inputs.forEach(input => {
            const name = input.name;
            if (name && name.includes('equipment')) {
                const key = name.split('[')[2].replace(']', '');
                equipmentData[key] = input.value;
            }
        });
        
        if (equipmentData.name && equipmentData.name.trim()) {
            equipment.push(equipmentData);
        }
    });
    
    // Add equipment to form data
    formData.append('equipment', JSON.stringify(equipment));
    
    // Submit form
    fetch(this.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم تحديث الموقع بنجاح', 'success');
            setTimeout(() => {
                window.location.href = "{{ url_for('locations.view_location', location_id=location.id) }}";
            }, 1500);
        } else {
            showNotification(data.message || 'حدث خطأ أثناء تحديث الموقع', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('حدث خطأ أثناء تحديث الموقع', 'error');
    });
});

// Function to remove instructions file
function removeInstructionsFile() {
    if (confirm('هل أنت متأكد من حذف ملف التعليمات؟')) {
        // Hide current file info
        document.querySelector('.current-file-info').style.display = 'none';

        // Set hidden field to indicate file removal
        document.getElementById('remove_instructions_file').value = 'true';

        // Update help text
        const helpText = document.querySelector('.file-upload-section .form-text');
        helpText.innerHTML = '<i class="fas fa-info-circle"></i> يمكنك رفع ملف PDF جديد يحتوي على تعليمات وإرشادات خاصة بهذا الموقع';

        showNotification('سيتم حذف ملف التعليمات عند حفظ التغييرات', 'warning');
    }
}

// دالة تحديث قائمة الأفراد
function refreshPersonnelList() {
    // إعادة تحميل الصفحة لتحديث قائمة الأفراد
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}
</script>
{% endblock %}
